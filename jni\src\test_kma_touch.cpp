/*
 * test_kma_touch.cpp - KMA内核触摸集成测试
 * 
 * 用于测试TouchHelperA与KMA驱动内核触摸的集成是否正常工作
 */

#include "ImGui/TouchHelperA.h"
#include "辅助类.h"
#include <iostream>
#include <unistd.h>

using namespace std;

int test_kma_touch_main()
{
    cout << "=== KMA内核触摸集成测试 ===" << endl;
    
    try {
        // 创建Kernel实例（会自动初始化KMA驱动）
        Kernel kernel;
        cout << "✓ KMA驱动初始化成功" << endl;
        
        // 初始化触摸系统
        int screenWidth = 1080;
        int screenHeight = 2340;
        uint32_t orientation = 0;
        bool readOnly = false;
        
        if (Touch_Init(screenWidth, screenHeight, orientation, readOnly)) {
            cout << "✓ KMA内核触摸初始化成功" << endl;
            
            // 测试基本触摸功能
            cout << "\n--- 测试基本触摸功能 ---" << endl;
            
            // 测试单点触摸
            cout << "测试单点触摸..." << endl;
            Touch_Down(540, 1170); // 屏幕中心
            usleep(100000); // 100ms
            Touch_Up();
            cout << "✓ 单点触摸测试完成" << endl;
            
            // 测试滑动
            cout << "测试滑动..." << endl;
            Touch_Down(300, 1000);
            usleep(50000);
            Touch_Move(400, 1100);
            usleep(50000);
            Touch_Move(500, 1200);
            usleep(50000);
            Touch_Up();
            cout << "✓ 滑动测试完成" << endl;
            
            // 测试KMA扩展功能
            cout << "\n--- 测试KMA扩展功能 ---" << endl;
            
            // 测试单击事件
            cout << "测试单击事件..." << endl;
            Touch_Click(540, 1170, 100);
            cout << "✓ 单击事件测试完成" << endl;
            
            // 测试滑动事件
            cout << "测试滑动事件..." << endl;
            Touch_Swipe(200, 1000, 800, 1000, 15, 20);
            cout << "✓ 滑动事件测试完成" << endl;
            
            // 测试多点触摸序列
            cout << "\n--- 测试多点触摸序列 ---" << endl;
            
            // 模拟游戏操作：点击、移动、射击
            cout << "模拟游戏操作序列..." << endl;
            
            // 点击移动按钮
            Touch_Click(200, 1800, 50);
            usleep(100000);
            
            // 滑动视角
            Touch_Swipe(600, 800, 700, 700, 10, 15);
            usleep(200000);
            
            // 点击射击按钮
            Touch_Click(900, 1800, 80);
            usleep(100000);
            
            cout << "✓ 游戏操作序列测试完成" << endl;
            
            // 测试随机触摸
            cout << "\n--- 测试随机触摸 ---" << endl;
            for (int i = 0; i < 5; i++) {
                float randomX = 200 + (rand() % 600);
                float randomY = 500 + (rand() % 1000);
                cout << "随机触摸点 " << (i+1) << ": (" << randomX << ", " << randomY << ")" << endl;
                Touch_Click(randomX, randomY, 30);
                usleep(200000);
            }
            cout << "✓ 随机触摸测试完成" << endl;
            
        } else {
            cout << "✗ KMA内核触摸初始化失败" << endl;
            return -1;
        }
        
    } catch (const exception& e) {
        cout << "✗ 测试过程中发生异常: " << e.what() << endl;
        return -1;
    }
    
    cout << "\n=== 所有测试完成 ===" << endl;
    cout << "KMA内核触摸功能正常工作！" << endl;
    return 0;
}
