{"files.associations": {"fstream": "cpp", "xlocmes": "cpp", "xstring": "cpp", "vector": "cpp", "array": "cpp", "initializer_list": "cpp", "list": "cpp", "random": "cpp", "utility": "cpp", "xhash": "cpp", "xtree": "cpp", "xutility": "cpp", "string": "cpp", "cmath": "cpp", "xlocinfo": "cpp", "algorithm": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "chrono": "cpp", "clocale": "cpp", "compare": "cpp", "concepts": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "format": "cpp", "forward_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "locale": "cpp", "map": "cpp", "memory": "cpp", "mutex": "cpp", "new": "cpp", "optional": "cpp", "ostream": "cpp", "ratio": "cpp", "set": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "xfacet": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xtr1common": "cpp"}}