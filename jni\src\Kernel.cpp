/*
 * Kernel.cpp - 内核读写类实现
 *
 * 修改说明：
 * 1. 已对接KMA驱动的driver.h内核读取功能
 * 2. 使用Driver类替代原有的ioctl方式进行内存操作
 * 3. 支持硬件级安全读取、CPU亲和设置等高级功能
 * 4. 保持原有接口兼容性，内部实现使用KMA驱动
 *
 * KMA驱动特性：
 * - 支持内核版本4.9~6.6
 * - 硬件级读取，指数级安全
 * - 支持多线程操作
 * - 内置防检测机制
 */

#include "辅助类.h"
#include "driver.h"
#include <sys/sysmacros.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>

// 全局KMA驱动实例
static Driver* g_driver = nullptr;

// 注释：get_dev方法已被移除，因为我们现在使用KMA驱动
// KMA驱动会自动处理设备连接和初始化

bool Kernel::init_key(char *key)
{
	// 使用KMA驱动初始化密钥
	if (g_driver != nullptr)
	{
		g_driver->initkey(key);
		return true;
	}
	return false;
}

Kernel::Kernel()
{
	// 初始化KMA驱动
	g_driver = new Driver();
	if (g_driver == nullptr)
	{
		printf("[-] KMA驱动初始化失败……\n");
		exit(0);
	}
	else
	{
		printf("\n[-] KMA驱动已经启动\n");
		printf("[-] KMA驱动安全守护已激活\n");
	}
}

Kernel::~Kernel()
{
	if (g_driver != nullptr)
	{
		delete g_driver;
		g_driver = nullptr;
	}
}

void Kernel::初始化读写(int pid)
{
	this->pid = pid;
	if (this->pid <= 0)
	{
		cout << "[-] 选定进程失败\n";
		return;
	}

	// 使用KMA驱动初始化进程ID
	if (g_driver != nullptr && g_driver->initpid(pid))
	{
		cout << "[-] KMA驱动读写初始化成功\n";
	}
	else
	{
		cout << "[-] KMA驱动读写初始化失败\n";
	}
}

bool Kernel::readv(uintptr_t addr, void *buffer, size_t size)
{
	// 使用KMA驱动进行内存读取
	if (g_driver != nullptr)
	{
		return g_driver->read(addr, buffer, size);
	}
	return false;
}

bool Kernel::writev(uintptr_t addr, void *buffer, size_t size)
{
	// 使用KMA驱动进行内存写入
	if (g_driver != nullptr)
	{
		return g_driver->write(addr, buffer, size);
	}
	return false;
}

uintptr_t Kernel::get_module_base(char *name)
{
	// 使用KMA驱动获取模块基址
	if (g_driver != nullptr)
	{
		return g_driver->get_module_base(this->pid, name);
	}
	return 0;
}

int Kernel::WriteDword(long int addr, int value)
{
	writev(addr, &value, 4);
	return 0;
}

float Kernel::WriteFloat(long int addr, float value)
{
	writev(addr, &value, 4);
	return 0;
}

template <typename T>
T Kernel::Read(uintptr_t addr)
{
	T data;
	readv(addr, &data, sizeof(T));
	return data;
}

float Kernel::getFloat(uintptr_t addr)
{
	float var = 0;
	readv(addr, &var, 4);
	return var;
}

int Kernel::getDword(uintptr_t addr)
{
	int var = 0;
	readv(addr, &var, 4);
	return var;
}

uintptr_t Kernel::getPtr32(uintptr_t addr)
{
	unsigned int var = 0;
	readv(addr & 0xFFFFFFFFFF, &var, 4);
	return (var & 0xFFFFFFFFFF);
}

uintptr_t Kernel::getPtr64(uintptr_t addr)
{
	unsigned long var = 0;
	readv(addr, &var, 8);
	return (var);
}
char Kernel::getByte(unsigned long addr)
{
	char var = 0;
	readv(addr, &var, 1);
	return (var);
}

void Kernel::writefloat(unsigned long addr, float data)
{
	writev(addr, &data, 4);
}

void Kernel::writedword(unsigned long addr, int data)
{
	writev(addr, &data, 4);
}

void Kernel::writeptr(unsigned long addr, uintptr_t data)
{
	writev(addr, &data, 8);
}

int Kernel::getPID(const char *packageName)
{
	// 使用KMA驱动获取进程PID
	if (g_driver != nullptr)
	{
		// 将const char*转换为char*（注意：这里假设packageName不会被修改）
		char *name = const_cast<char*>(packageName);
		pid_t pid = g_driver->get_pid(name);
		return (int)pid;
	}

	// 如果KMA驱动不可用，回退到原始方法
	int id = -1;
	DIR *dir;
	FILE *fp;
	char filename[64];
	char cmdline[64];
	struct dirent *entry;
	dir = opendir("/proc");
	while ((entry = readdir(dir)) != NULL)
	{
		id = atoi(entry->d_name);
		if (id != 0)
		{
			sprintf(filename, "/proc/%d/cmdline", id);
			fp = fopen(filename, "r");
			if (fp)
			{
				fgets(cmdline, sizeof(cmdline), fp);
				fclose(fp);
				if (strcmp(packageName, cmdline) == 0)
				{
					return id;
				}
			}
		}
	}
	closedir(dir);
	return -1;
}

void Kernel::getUTF8(char *buf, unsigned long namepy)
{
	unsigned short buf16[16] = {0};
	readv(namepy, buf16, 28);
	unsigned short *pTempUTF16 = buf16;
	char *pTempUTF8 = buf;
	char *pUTF8End = pTempUTF8 + 32;
	while (pTempUTF16 < pTempUTF16 + 28)
	{
		if (*pTempUTF16 <= 0x007F && pTempUTF8 + 1 < pUTF8End)
		{
			*pTempUTF8++ = (char)*pTempUTF16;
		}
		else if (*pTempUTF16 >= 0x0080 && *pTempUTF16 <= 0x07FF && pTempUTF8 + 2 < pUTF8End)
		{
			*pTempUTF8++ = (*pTempUTF16 >> 6) | 0xC0;
			*pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
		}
		else if (*pTempUTF16 >= 0x0800 && *pTempUTF16 <= 0xFFFF && pTempUTF8 + 3 < pUTF8End)
		{
			*pTempUTF8++ = (*pTempUTF16 >> 12) | 0xE0;
			*pTempUTF8++ = ((*pTempUTF16 >> 6) & 0x3F) | 0x80;
			*pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
		}
		else
		{
			break;
		}
		pTempUTF16++;
	}
}

// ========== KMA驱动扩展功能 ==========

// 安全读取内存（硬件级别）
bool Kernel::read_safe(uintptr_t addr, void *buffer, size_t size)
{
	if (g_driver != nullptr)
	{
		return g_driver->read_safe(addr, buffer, size);
	}
	return false;
}

// 获取进程PID（支持comm字段）
int Kernel::getPID(const char *packageName, const char *comm)
{
	if (g_driver != nullptr)
	{
		char *name = const_cast<char*>(packageName);
		char *commField = const_cast<char*>(comm);
		pid_t pid = g_driver->get_pid(name, commField);
		return (int)pid;
	}
	return -1;
}

// 获取模块基址（支持大小参数）
uintptr_t Kernel::get_module_base_with_size(char *name, size_t size)
{
	if (g_driver != nullptr)
	{
		return g_driver->get_module_base(this->pid, name, size);
	}
	return 0;
}

// 设置CPU亲和性
void Kernel::setCPUAffinity(int cpuNum)
{
	if (g_driver != nullptr)
	{
		g_driver->cpuset(cpuNum);
	}
}

// 设置CPU亲和性范围
void Kernel::setCPUAffinityRange(int start, int end)
{
	if (g_driver != nullptr)
	{
		g_driver->cpuset(start, end);
	}
}

// 模板方法：安全读取
template <typename T>
T Kernel::read_safe(uintptr_t addr)
{
	if (g_driver != nullptr)
	{
		return g_driver->read_safe<T>(addr);
	}
	T defaultValue{};
	return defaultValue;
}

// 模板方法：写入数据
template <typename T>
bool Kernel::write_template(uintptr_t addr, T value)
{
	if (g_driver != nullptr)
	{
		return g_driver->write<T>(addr, value);
	}
	return false;
}