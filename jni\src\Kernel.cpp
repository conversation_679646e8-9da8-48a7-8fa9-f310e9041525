#include "辅助类.h"
#include <sys/sysmacros.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>

int Kernel::symbol_file(const char *filename)
{
	// 判断文件名是否含小写并且不含大写不含数字不含符号
	int length = strlen(filename);
	for (int i = 0; i < length; i++)
	{
		if (islower(filename[i]))
		{
			has_lower = 1;
		}
		else if (isupper(filename[i]))
		{
			has_upper = 1;
		}
		else if (ispunct(filename[i]))
		{
			has_symbol = 1;
		}
		else if (isdigit(filename[i]))
		{
			has_digit = 1;
		}
	}
	// return has_lower && !has_upper && !has_symbol && !has_digit;
	return has_upper && !has_lower && !has_symbol && !has_digit;
}

// 执行系统命令并返回结果
char *Kernel::execCom(const char *cmd)
{
	char *buffer = (char *)malloc(4096);
	if (buffer == NULL)
	{
		return NULL;
	}

	FILE *pipe = popen(cmd, "r");
	if (!pipe)
	{
		free(buffer);
		return NULL;
	}

	char *result = fgets(buffer, 4096, pipe);
	pclose(pipe);

	if (result == NULL)
	{
		free(buffer);
		return NULL;
	}

	return buffer;
}

// 查找匹配正则表达式的第一个路径
bool Kernel::findFirstMatchingPath(const char *dirPath, regex_t *regex, char *result)
{
	DIR *dir;
	struct dirent *entry;
	if ((dir = opendir(dirPath)) != NULL)
	{
		while ((entry = readdir(dir)) != NULL)
		{
			// 跳过 . 和 ..
			if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0)
			{
				continue;
			}
			
			// 构建完整路径
			char fullPath[1024]; // 适当调整数组大小
			snprintf(fullPath, sizeof(fullPath), "%s/%s", dirPath, entry->d_name);
			
			// 检查是否是符号链接
			if (entry->d_type == DT_LNK)
			{
				char linkPath[1024]; // 符号链接指向的路径
				ssize_t len = readlink(fullPath, linkPath, sizeof(linkPath) - 1);
				if (len != -1)
				{
					linkPath[len] = '\0'; // 添加字符串结束符
					// 检查符号链接指向的路径是否匹配正则表达式
					if (regexec(regex, linkPath, 0, NULL, 0) == 0)
					{
						strcpy(result, fullPath);
						closedir(dir);
						return true;
					}
				}
				else
				{
					perror("readlink");
				}
			}
		}
		closedir(dir);
	}
	else
	{
		perror("Unable to open directory");
	}
	
	return false;
}

// 创建设备节点
void Kernel::createDriverNode(const char *path, int major, int minor)
{
	// 使用系统命令创建设备节点，而不是直接调用mknod
	char cmd[256];
	sprintf(cmd, "mknod %s c %d %d", path, major, minor);
	system(cmd);
}

// 移除设备节点
void Kernel::removeDeviceNode(const char *path)
{
	if (unlink(path) == -1)
	{
		fprintf(stderr, "Failed to remove device node: %s\n", strerror(errno));
	}
}

int Kernel::get_dev()
{
	char *output = execCom("ls -l /proc/*/exe 2>/dev/null | grep -E \"/data/[a-z]{6} \\(deleted\\)\"");
	char filePath[256] = {0};
	char pid[56] = {0};
	
	if (output != NULL)
	{
		char *procStart = strstr(output, "/proc/");
		if (procStart == NULL) {
			free(output);
			return -1;
		}
		
		char *pidStart = procStart + 6;
		char *pidEnd = strchr(pidStart, '/');
		if (pidEnd == NULL) {
			free(output);
			return -1;
		}
		
		strncpy(pid, pidStart, pidEnd - pidStart);
		pid[pidEnd - pidStart] = '\0';
		
		char *arrowStart = strstr(output, "->");
		if (arrowStart == NULL) {
			free(output);
			return -1;
		}
		
		char *start = arrowStart + 3;
		char *end = strchr(output, '(');
		if (end == NULL) {
			free(output);
			return -1;
		}
		end = end - 1;
		
		strncpy(filePath, start, end - start + 1);
		filePath[end - start] = '\0';
		
		char *replacePtr = strstr(filePath, "data");
		if (replacePtr != NULL)
		{
			memmove(replacePtr + 2, replacePtr + 3, strlen(replacePtr + 3) + 1);
			memmove(replacePtr, "dev", strlen("dev"));
		}
		
		free(output);
	}
	else
	{
		return -1;
	}
	
	char fdPath[256];
	char pattern[100];
	snprintf(pattern, sizeof(pattern), ".*%s.*", filePath + 5);
	
	int major_number = 0;
	snprintf(fdPath, sizeof(fdPath), "/proc/%s/fd", pid);
	
	regex_t regex;
	if (regcomp(&regex, pattern, 0) != 0)
	{
		return -1;
	}
	
	char result[1024] = {0};
	if (findFirstMatchingPath(fdPath, &regex, result))
	{
		char cmd[256];
		sprintf(cmd, "ls -AL -l %s | grep -Eo '[0-9]{3},' | grep -Eo '[0-9]{3}'", result);
		
		char *fdInfo = execCom(cmd);
		if (fdInfo != NULL && strlen(fdInfo) > 0)
		{
			fdInfo[strlen(fdInfo) - 1] = '\0';
			major_number = atoi(fdInfo);
			free(fdInfo);
		}
		else
		{
			regfree(&regex);
			return -1;
		}
	}
	else
	{
		regfree(&regex);
		return -1;
	}
	
	regfree(&regex);
	
	if (strlen(filePath) > 0)
	{
		createDriverNode(filePath, major_number, 0);
		
		fd = open(filePath, O_RDWR);
		if (fd == -1)
		{
			removeDeviceNode(filePath);
			return -1;
		}
		else
		{
			removeDeviceNode(filePath);
			return 1;
		}
	}
	
	return -1;
}

bool Kernel::init_key(char *key)
{
	char buf[0x100];
	strcpy(buf, key);
	if (ioctl(fd, OP_INIT_KEY, buf) != 0)
	{
		return false;
	}
	return true;
}

Kernel::Kernel()
{
	if (get_dev() == -1)
	{
		printf("[-] 驱动链接失败……\n");
		exit(0);
	}
	else
	{
		printf("\n[-] 驱动已经启动\n");
		printf("[-] 驱动安全守护已激活\n");
	}
}

Kernel::~Kernel()
{
	if (fd > 0)
		close(fd);
}

void Kernel::初始化读写(int pid)
{
	this->pid = pid;
	if (this->pid <= 0)
	{
		cout << "[-] 选定进程失败\n";
	}
	else
	{
		cout << "[-] 读写初始化成功\n";
	}
}

bool Kernel::readv(uintptr_t addr, void *buffer, size_t size)
{
	COPY_MEMORY cm;
	cm.pid = this->pid;
	cm.addr = addr;
	cm.buffer = buffer;
	cm.size = size;
	if (ioctl(fd, OP_READ_MEM, &cm) != 0)
	{
		return false;
	}
	return true;
}

bool Kernel::writev(uintptr_t addr, void *buffer, size_t size)
{
	COPY_MEMORY cm;
	cm.pid = this->pid;
	cm.addr = addr;
	cm.buffer = buffer;
	cm.size = size;
	if (ioctl(fd, OP_WRITE_MEM, &cm) != 0)
	{
		return false;
	}
	return true;
}

uintptr_t Kernel::get_module_base(char *name)
{
	MODULE_BASE mb;
	char buf[0x100];
	strcpy(buf, name);
	mb.pid = this->pid;
	mb.name = buf;

	if (ioctl(fd, OP_MODULE_BASE, &mb) != 0)
	{
		return 0;
	}
	return mb.base;
}

int Kernel::WriteDword(long int addr, int value)
{
	writev(addr, &value, 4);
	return 0;
}

float Kernel::WriteFloat(long int addr, float value)
{
	writev(addr, &value, 4);
	return 0;
}

template <typename T>
T Kernel::Read(uintptr_t addr)
{
	T data;
	readv(addr, &data, sizeof(T));
	return data;
}

float Kernel::getFloat(uintptr_t addr)
{
	float var = 0;
	readv(addr, &var, 4);
	return var;
}

int Kernel::getDword(uintptr_t addr)
{
	int var = 0;
	readv(addr, &var, 4);
	return var;
}

uintptr_t Kernel::getPtr32(uintptr_t addr)
{
	unsigned int var = 0;
	readv(addr & 0xFFFFFFFFFF, &var, 4);
	return (var & 0xFFFFFFFFFF);
}

uintptr_t Kernel::getPtr64(uintptr_t addr)
{
	unsigned long var = 0;
	readv(addr, &var, 8);
	return (var);
}
char Kernel::getByte(unsigned long addr)
{
	char var = 0;
	readv(addr, &var, 1);
	return (var);
}

void Kernel::writefloat(unsigned long addr, float data)
{
	writev(addr, &data, 4);
}

void Kernel::writedword(unsigned long addr, int data)
{
	writev(addr, &data, 4);
}

void Kernel::writeptr(unsigned long addr, uintptr_t data)
{
	writev(addr, &data, 8);
}

int Kernel::getPID(const char *packageName)
{
	int id = -1;
	DIR *dir;
	FILE *fp;
	char filename[64];
	char cmdline[64];
	struct dirent *entry;
	dir = opendir("/proc");
	while ((entry = readdir(dir)) != NULL)
	{
		id = atoi(entry->d_name);
		if (id != 0)
		{
			sprintf(filename, "/proc/%d/cmdline", id);
			fp = fopen(filename, "r");
			if (fp)
			{
				fgets(cmdline, sizeof(cmdline), fp);
				fclose(fp);
				if (strcmp(packageName, cmdline) == 0)
				{
					return id;
				}
			}
		}
	}
	closedir(dir);
	return -1;
}

void Kernel::getUTF8(char *buf, unsigned long namepy)
{
	unsigned short buf16[16] = {0};
	readv(namepy, buf16, 28);
	unsigned short *pTempUTF16 = buf16;
	char *pTempUTF8 = buf;
	char *pUTF8End = pTempUTF8 + 32;
	while (pTempUTF16 < pTempUTF16 + 28)
	{
		if (*pTempUTF16 <= 0x007F && pTempUTF8 + 1 < pUTF8End)
		{
			*pTempUTF8++ = (char)*pTempUTF16;
		}
		else if (*pTempUTF16 >= 0x0080 && *pTempUTF16 <= 0x07FF && pTempUTF8 + 2 < pUTF8End)
		{
			*pTempUTF8++ = (*pTempUTF16 >> 6) | 0xC0;
			*pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
		}
		else if (*pTempUTF16 >= 0x0800 && *pTempUTF16 <= 0xFFFF && pTempUTF8 + 3 < pUTF8End)
		{
			*pTempUTF8++ = (*pTempUTF16 >> 12) | 0xE0;
			*pTempUTF8++ = ((*pTempUTF16 >> 6) & 0x3F) | 0x80;
			*pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
		}
		else
		{
			break;
		}
		pTempUTF16++;
	}
}