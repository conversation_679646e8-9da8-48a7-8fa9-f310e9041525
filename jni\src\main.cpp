#include <Draw.h>
#include <limits.h>
#include <sys/stat.h>
#include <unistd.h>
#include <cstdio>
#include <fstream>
#include <iostream>
#include <random>
#include <set>
#include "辅助类.h"

#include <dirent.h>
#include <dlfcn.h>
#include <fcntl.h>
#include <limits.h>
#include <malloc.h>
#include <pthread.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/inotify.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include <sys/ptrace.h>
#include <sys/resource.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <time.h>
#include <unistd.h>
#include <csignal>
#include <cstdlib>
#include <ctime>
#include <fstream>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#include "图片调用.h"
#include "res/go.h"
using namespace std;
#define RESET_TEXT "\033[0m"
#define PINK_TEXT "\033[35m"
#define BOLD_TEXT "\033[1m"
int abs_ScreenX, abs_ScreenY;

int 无后台, 自瞄选项, 漏打;

布局 布局;
绘制 绘制;

int main(int argc, char *argv[]) __attribute((__annotate__(("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3"))))
{
    yanzheng();

    std::ifstream inputFile("settings.conf");
    if (inputFile.is_open())
    {
        inputFile >> 绘制.防录屏 >> 绘制.无后台开关;
        inputFile.close();
        std::cout << BOLD_TEXT << PINK_TEXT << "[+] 已加载上次配置！如需修改删除settings.conf" << RESET_TEXT << std::endl;
    }
    else
    {
        std::cout << BOLD_TEXT << PINK_TEXT << "[+] 不懂就都输入1！！" << RESET_TEXT << std::endl;
        std::cout << BOLD_TEXT << PINK_TEXT << "[1]开启防录屏/[2]关闭防录屏 请输入 (1/2): " << RESET_TEXT;
        std::cin >> 绘制.防录屏;
        std::cout << BOLD_TEXT << PINK_TEXT << "[1]开启无后台/[2]关闭无后台 请输入 (1/2): " << RESET_TEXT;
        std::cin >> 绘制.无后台开关;
        std::ofstream outputFile("settings.conf");
        if (outputFile.is_open())
        {
            outputFile << 绘制.防录屏 << " " << 绘制.无后台开关;
            outputFile.close();
        }
    }

    绘制.自瞄模式 = 0;   // std::stoi(argv[2]);
    绘制.漏打模式 = 0;   // std::stoi(argv[4]);

    printf("\033[37;1m");

    if (绘制.无后台开关 == 0)
    {
    }
    else
    {
        pid_t pid = fork();
        if (pid > 0)
        {
            exit(0);
        }
    }
    布局.初始化程序();
    加载内存图片();
    if (绘制.自瞄模式 == 0)
    {
        绘制.自瞄.预判力度 = 1.55f;
        绘制.自瞄主线程();
        绘制.GetTouch();
        绘制.按钮.自瞄选项 = true;
    }
    绘制.读取配置();
    布局.开启悬浮窗();
}