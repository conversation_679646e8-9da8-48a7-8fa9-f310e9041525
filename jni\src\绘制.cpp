#include <algorithm>
#include <array>
#include <cmath>
#include <cstdlib>
#include <map>
#include <sstream>
#include <unordered_map>
#include <vector>
#include "图片调用.h"
#include "物资ID.h"
#include "辅助类.h"
#include "Utils/PtraceUtils.h"
#include <memory>
#include <mutex>
#include <random>
#include <android/log.h>
#define LOG_TAG "MyApp" // 定义日志标签
#define LOGI(...) ((void)__android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__))
extern 绘制 绘制;

char filename[64];
const char *MEMReadandwrite = "/storage/emulated/0/Download/Memory";
int MountFile;

std::mutex mtx;
unordered_map<int, ImColor> colorMap;
std::mutex printMutex;
std::map<std::string, int> Antitankgrenade; // 给手雷一个机会,不然让你飞起来

void 绘制::readmaps_v(struct map_node **head)
{
  char lj[32], buff[256];
  FILE *fp;
  sprintf(lj, "/proc/%d/maps", pid);
  fp = fopen(lj, "r");
  if (fp == NULL)
  {
    return;
  }
  while (fgets(buff, sizeof(buff), fp) != NULL)
  {
    if (strstr(buff, "rw-p 00000000 00:00 0") != NULL)
    {
      unsigned long start_addr, end_addr;
      sscanf(buff, "%lx-%lx", &start_addr, &end_addr);
      long int count = (end_addr - start_addr) / 0x4;
      if (count > 解密模式)
        continue;
      struct map_node *node = (struct map_node *)malloc(sizeof(struct map_node));
      node->start_addr = start_addr;
      node->end_addr = end_addr;
      node->next = NULL;
      if (*head == NULL)
      {
        *head = node;
      }
      else
      {
        struct map_node *cur = *head;
        while (cur->next != NULL)
        {
          cur = cur->next;
        }
        cur->next = node;
      }
    }
  }
  fclose(fp);
}

void 绘制::free_maps(struct map_node *head)
{
  struct map_node *cur = head;
  while (cur != NULL)
  {
    struct map_node *temp = cur;
    cur = cur->next;
    free(temp);
  }
}

void 绘制::print_maps(struct map_node *head)
{
  struct map_node *cur = head;
  long int Objaddr, 循环次数 = 0;
  long int Arrayheader, Arrayheader_is = 0;
  int array_is, flagso = 0;
  bool jie_start = false;
  while (cur != NULL)
  {
    Objaddr = 0;
    循环次数 = 0;
    while (Objaddr <= cur->end_addr)
    {
      Objaddr = cur->start_addr + 0x4 * 循环次数;
      if (读写.getPtr64(Objaddr) == 地址.自身地址)
      {
        array_is = 0;
        flagso = 0;
        Arrayheader_is = Objaddr - 0x4;
        for (int i = 0; i < 100; i++)
        {
          Arrayheader = Arrayheader_is - (0x8 * i);
          int Array_size = 读写.getDword(Arrayheader);
          if (Array_size == 0)
          {
            flagso++;
          }
          if (Array_size >= 100 && Array_size <= 150)
          {
            array_is++;
          }
          if (Array_size == 0 && flagso == 1 && array_is == 48)
          {
            jie_start = true;
            解密数组 = Arrayheader + 0x4;
            break;
          }
        }
      }
      if (jie_start)
        break;
      循环次数++;
    }
    if (jie_start)
      break;
    cur = cur->next;
  }
}

void 绘制::InitMaps()
{
  struct map_node *head = NULL;
  readmaps_v(&head);
  print_maps(head);
  free_maps(head);
}

void 绘制::保存配置()
{
  // 创建配置文件夹(如果不存在)
  system("mkdir -p /data/config");

  // 使用单个二进制文件保存所有配置
  FILE *fp = fopen("/data/config/settings.bin", "wb");
  if (!fp)
    return;

  // 1. 保存float类型配置
  fwrite(&自瞄.自瞄范围, sizeof(float), 1, fp);
  fwrite(&自瞄.触摸范围, sizeof(float), 1, fp);
  fwrite(&自瞄.自瞄速度, sizeof(float), 1, fp);
  fwrite(&自瞄.压枪力度, sizeof(float), 1, fp);
  fwrite(&自瞄.预判力度, sizeof(float), 1, fp);
  fwrite(&按钮.雷达X, sizeof(float), 1, fp);
  fwrite(&按钮.雷达Y, sizeof(float), 1, fp);
  fwrite(&自瞄.趴下位置调节, sizeof(float), 1, fp);
  fwrite(&自瞄.触摸采样率, sizeof(float), 1, fp);
  fwrite(&自瞄.喷子距离限制, sizeof(float), 1, fp);
  fwrite(&自瞄.掉血自瞄数率, sizeof(float), 1, fp);
  fwrite(&自瞄.腰射距离限制, sizeof(float), 1, fp);
  fwrite(&自瞄.自瞄距离限制, sizeof(float), 1, fp);
  fwrite(&按钮.方框粗细, sizeof(float), 1, fp);
  fwrite(&按钮.射线粗细, sizeof(float), 1, fp);
  fwrite(&按钮.骨骼粗细, sizeof(float), 1, fp);
  fwrite(&骨骼距离限制, sizeof(float), 1, fp);
  fwrite(&按钮.速度值, sizeof(float), 1, fp);
  fwrite(&按钮.第三人称, sizeof(float), 1, fp);

  // 2. 保存int类型配置
  fwrite(&自瞄.自瞄条件, sizeof(int), 1, fp);
  fwrite(&自瞄.充电口方向, sizeof(int), 1, fp);
  fwrite(&按钮.帧率选项, sizeof(int), 1, fp);
  fwrite(&按钮.当前帧率, sizeof(int), 1, fp);
  // 添加手持样式和血条样式
  fwrite(&按钮.手持绘图, sizeof(int), 1, fp);
  fwrite(&按钮.血条绘图, sizeof(int), 1, fp);

  // 3. 保存数组数据
  fwrite(自瞄.站压枪, sizeof(float), 100, fp);
  fwrite(自瞄.趴压枪, sizeof(float), 100, fp);
  fwrite(自瞄.倍镜力度, sizeof(float), 100, fp);

  // 4. 保存bool类型配置
  // 绘制相关开关
  fwrite(&按钮.绘制, sizeof(bool), 1, fp);
  fwrite(&按钮.忽略人机, sizeof(bool), 1, fp);
  fwrite(&按钮.人数, sizeof(bool), 1, fp);
  fwrite(&按钮.方框, sizeof(bool), 1, fp);
  fwrite(&按钮.血量, sizeof(bool), 1, fp);
  fwrite(&按钮.手持, sizeof(bool), 1, fp);
  fwrite(&按钮.背敌预警, sizeof(bool), 1, fp);
  fwrite(&按钮.距离, sizeof(bool), 1, fp);
  fwrite(&按钮.射线, sizeof(bool), 1, fp);
  fwrite(&按钮.名字, sizeof(bool), 1, fp);
  fwrite(&按钮.骨骼, sizeof(bool), 1, fp);
  fwrite(&按钮.雷达, sizeof(bool), 1, fp);

  // 物资绘制开关
  fwrite(&按钮.绘制投掷, sizeof(bool), 1, fp);
  fwrite(&按钮.绘制载具, sizeof(bool), 1, fp);
  fwrite(&按钮.绘制防具, sizeof(bool), 1, fp);
  fwrite(&按钮.绘制道具, sizeof(bool), 1, fp);
  fwrite(&按钮.绘制盒子, sizeof(bool), 1, fp);
  fwrite(&按钮.绘制药品, sizeof(bool), 1, fp);
  fwrite(&按钮.绘制子弹, sizeof(bool), 1, fp);
  fwrite(&按钮.绘制武器, sizeof(bool), 1, fp);
  fwrite(&按钮.绘制配件, sizeof(bool), 1, fp);
  fwrite(&按钮.绘制地铁, sizeof(bool), 1, fp);

  // 自瞄相关开关
  fwrite(&自瞄.初始化, sizeof(bool), 1, fp);
  fwrite(&自瞄.隐藏自瞄圈, sizeof(bool), 1, fp);
  fwrite(&自瞄.随机触摸点, sizeof(bool), 1, fp);
  fwrite(&自瞄.触摸位置, sizeof(bool), 1, fp);
  fwrite(&自瞄.动态自瞄, sizeof(bool), 1, fp);
  fwrite(&自瞄.准星射线, sizeof(bool), 1, fp);
  fwrite(&自瞄.倒地不瞄, sizeof(bool), 1, fp);
  fwrite(&自瞄.掉血自瞄, sizeof(bool), 1, fp);
  fwrite(&自瞄.自瞄控件, sizeof(bool), 1, fp);
  fwrite(&自瞄.喷子自瞄, sizeof(bool), 1, fp);
  fwrite(&自瞄.狙击自瞄, sizeof(bool), 1, fp);
  fwrite(&自瞄.人机不瞄, sizeof(bool), 1, fp);
  fwrite(&自瞄.框内自瞄, sizeof(bool), 1, fp);

  // 5. 保存颜色配置
  fwrite(Colorset, sizeof(ColorTable), 2, fp);
  fwrite(&手持颜色, sizeof(ImVec4), 1, fp);
  fwrite(&物资颜色, sizeof(ImVec4), 1, fp);
  fwrite(&车辆颜色, sizeof(ImVec4), 1, fp);

  // 确保数据写入磁盘
  fflush(fp);
  fsync(fileno(fp));
  fclose(fp);
}

void 绘制::读取配置()
{
  FILE *fp = fopen("/data/config/settings.bin", "rb");
  if (!fp)
  {
    // 如果文件不存在，设置默认值
    for (int i = 0; i < 100; i++)
    {
      自瞄.站压枪[i] = 0.9f;
      自瞄.趴压枪[i] = 0.7f;
    }

    自瞄.压枪力度 = 1.7f;
    自瞄.趴下位置调节 = 0.9f;
    自瞄.自瞄速度 = 25.0f;
    // 设置默认倍镜力度
    自瞄.倍镜力度[0] = 1.0f; // 腰射
    自瞄.倍镜力度[1] = 1.5f; // 机瞄
    自瞄.倍镜力度[2] = 1.5f; // 红点
    自瞄.倍镜力度[3] = 1.5f; // 二倍
    自瞄.倍镜力度[4] = 1.3f; // 三倍
    自瞄.倍镜力度[5] = 1.3f; // 四倍
    自瞄.倍镜力度[6] = 1.3f; // 六倍
    自瞄.倍镜力度[7] = 1.3f; // 八倍

    自瞄.站压枪[0] = 0.9f;  // AKM
    自瞄.站压枪[1] = 0.6f;  // M16A4
    自瞄.站压枪[2] = 0.7f;  // SCAR-L
    自瞄.站压枪[3] = 0.7f;  // M416
    自瞄.站压枪[4] = 0.8f;  // Groza
    自瞄.站压枪[5] = 0.6f;  // AUG
    自瞄.站压枪[6] = 0.6f;  // QBZ
    自瞄.站压枪[7] = 0.8f;  // M762
    自瞄.站压枪[8] = 0.9f;  // Mk47
    自瞄.站压枪[9] = 0.5f;  // G36C
    自瞄.站压枪[10] = 0.3f; // AC-VAL
    自瞄.站压枪[11] = 0.8f; // 蜜獾
    自瞄.站压枪[12] = 0.3f; // FAMAS
    自瞄.站压枪[13] = 0.4f; // UZI
    自瞄.站压枪[14] = 0.3f; // UMP45
    自瞄.站压枪[15] = 0.4f; // Vector
    自瞄.站压枪[16] = 0.5f; // 汤姆逊
    自瞄.站压枪[17] = 0.3f; // 野牛
    自瞄.站压枪[18] = 0.4f; // MP5K
    自瞄.站压枪[19] = 0.3f; // P90
    自瞄.站压枪[20] = 0.5f; // SKS
    自瞄.站压枪[21] = 0.3f; // VSS
    自瞄.站压枪[22] = 0.3f; // MINI14
    自瞄.站压枪[23] = 0.9f; // MK14
    自瞄.站压枪[24] = 0.2f; // M417
    自瞄.站压枪[25] = 0.3f; // MK20
    自瞄.站压枪[26] = 0.5f; // M249
    自瞄.站压枪[27] = 0.3f; // DP-28
    自瞄.站压枪[28] = 0.3f; // MG3
    自瞄.站压枪[29] = 0.3f; // PKM

    // 趴下压枪预设
    自瞄.趴压枪[0] = 0.7f;  // AKM
    自瞄.趴压枪[1] = 0.7f;  // M16A4
    自瞄.趴压枪[2] = 0.5f;  // SCAR-L
    自瞄.趴压枪[3] = 0.5f;  // M416
    自瞄.趴压枪[4] = 1.5f;  // Groza
    自瞄.趴压枪[5] = 1.1f;  // AUG
    自瞄.趴压枪[6] = 1.3f;  // QBZ
    自瞄.趴压枪[7] = 1.6f;  // M762
    自瞄.趴压枪[8] = 1.4f;  // Mk47
    自瞄.趴压枪[9] = 0.6f;  // G36C
    自瞄.趴压枪[10] = 0.3f; // AC-VAL
    自瞄.趴压枪[11] = 1.6f; // 蜜獾
    自瞄.趴压枪[12] = 0.7f; // FAMAS
    自瞄.趴压枪[13] = 0.4f; // UZI
    自瞄.趴压枪[14] = 0.3f; // UMP45
    自瞄.趴压枪[15] = 0.5f; // Vector
    自瞄.趴压枪[16] = 0.6f; // 汤姆逊
    自瞄.趴压枪[17] = 0.4f; // 野牛
    自瞄.趴压枪[18] = 0.5f; // MP5K
    自瞄.趴压枪[19] = 0.3f; // P90
    自瞄.趴压枪[20] = 0.9f; // SKS
    自瞄.趴压枪[21] = 0.4f; // VSS
    自瞄.趴压枪[22] = 1.0f; // MINI14
    自瞄.趴压枪[23] = 0.8f; // MK14
    自瞄.趴压枪[24] = 0.0f; // M417
    自瞄.趴压枪[25] = 0.2f; // MK20
    自瞄.趴压枪[26] = 0.9f; // M249
    自瞄.趴压枪[27] = 0.4f; // DP-28
    自瞄.趴压枪[28] = 0.4f; // MG3
    自瞄.趴压枪[29] = 0.5f; // PKM
    // 设置默认样式
    按钮.手持绘图 = 0; // 默认手持武器文字
    按钮.血条绘图 = 0; // 默认样式
    return;
  }

  // 1. 读取float类型配置
  fread(&自瞄.自瞄范围, sizeof(float), 1, fp);
  fread(&自瞄.触摸范围, sizeof(float), 1, fp);
  fread(&自瞄.自瞄速度, sizeof(float), 1, fp);
  fread(&自瞄.压枪力度, sizeof(float), 1, fp);
  fread(&自瞄.预判力度, sizeof(float), 1, fp);
  fread(&按钮.雷达X, sizeof(float), 1, fp);
  fread(&按钮.雷达Y, sizeof(float), 1, fp);
  fread(&自瞄.趴下位置调节, sizeof(float), 1, fp);
  fread(&自瞄.触摸采样率, sizeof(float), 1, fp);
  fread(&自瞄.喷子距离限制, sizeof(float), 1, fp);
  fread(&自瞄.掉血自瞄数率, sizeof(float), 1, fp);
  fread(&自瞄.腰射距离限制, sizeof(float), 1, fp);
  fread(&自瞄.自瞄距离限制, sizeof(float), 1, fp);
  fread(&按钮.方框粗细, sizeof(float), 1, fp);
  fread(&按钮.射线粗细, sizeof(float), 1, fp);
  fread(&按钮.骨骼粗细, sizeof(float), 1, fp);
  fread(&骨骼距离限制, sizeof(float), 1, fp);
  fread(&按钮.速度值, sizeof(float), 1, fp);
  fread(&按钮.第三人称, sizeof(float), 1, fp);

  // 2. 读取int类型配置
  fread(&自瞄.自瞄条件, sizeof(int), 1, fp);
  fread(&自瞄.充电口方向, sizeof(int), 1, fp);
  fread(&按钮.帧率选项, sizeof(int), 1, fp);
  fread(&按钮.当前帧率, sizeof(int), 1, fp);
  // 读取手持样式和血条样式
  fread(&按钮.手持绘图, sizeof(int), 1, fp);
  fread(&按钮.血条绘图, sizeof(int), 1, fp);

  // 3. 读取数组数据
  fread(自瞄.站压枪, sizeof(float), 100, fp);
  fread(自瞄.趴压枪, sizeof(float), 100, fp);
  fread(自瞄.倍镜力度, sizeof(float), 100, fp);

  // 4. 读取bool类型配置
  // 绘制相关开关
  fread(&按钮.绘制, sizeof(bool), 1, fp);
  fread(&按钮.忽略人机, sizeof(bool), 1, fp);
  fread(&按钮.人数, sizeof(bool), 1, fp);
  fread(&按钮.方框, sizeof(bool), 1, fp);
  fread(&按钮.血量, sizeof(bool), 1, fp);
  fread(&按钮.手持, sizeof(bool), 1, fp);
  fread(&按钮.背敌预警, sizeof(bool), 1, fp);
  fread(&按钮.距离, sizeof(bool), 1, fp);
  fread(&按钮.射线, sizeof(bool), 1, fp);
  fread(&按钮.名字, sizeof(bool), 1, fp);
  fread(&按钮.骨骼, sizeof(bool), 1, fp);
  fread(&按钮.雷达, sizeof(bool), 1, fp);

  // 物资绘制开关
  fread(&按钮.绘制投掷, sizeof(bool), 1, fp);
  fread(&按钮.绘制载具, sizeof(bool), 1, fp);
  fread(&按钮.绘制防具, sizeof(bool), 1, fp);
  fread(&按钮.绘制道具, sizeof(bool), 1, fp);
  fread(&按钮.绘制盒子, sizeof(bool), 1, fp);
  fread(&按钮.绘制药品, sizeof(bool), 1, fp);
  fread(&按钮.绘制子弹, sizeof(bool), 1, fp);
  fread(&按钮.绘制武器, sizeof(bool), 1, fp);
  fread(&按钮.绘制配件, sizeof(bool), 1, fp);
  fread(&按钮.绘制地铁, sizeof(bool), 1, fp);

  // 自瞄相关开关
  fread(&自瞄.初始化, sizeof(bool), 1, fp);
  fread(&自瞄.隐藏自瞄圈, sizeof(bool), 1, fp);
  fread(&自瞄.随机触摸点, sizeof(bool), 1, fp);
  fread(&自瞄.触摸位置, sizeof(bool), 1, fp);
  fread(&自瞄.动态自瞄, sizeof(bool), 1, fp);
  fread(&自瞄.准星射线, sizeof(bool), 1, fp);
  fread(&自瞄.倒地不瞄, sizeof(bool), 1, fp);
  fread(&自瞄.掉血自瞄, sizeof(bool), 1, fp);
  fread(&自瞄.自瞄控件, sizeof(bool), 1, fp);
  fread(&自瞄.喷子自瞄, sizeof(bool), 1, fp);
  fread(&自瞄.狙击自瞄, sizeof(bool), 1, fp);
  fread(&自瞄.人机不瞄, sizeof(bool), 1, fp);
  fread(&自瞄.框内自瞄, sizeof(bool), 1, fp);

  // 5. 读取颜色配置
  fread(Colorset, sizeof(ColorTable), 2, fp);
  fread(&手持颜色, sizeof(ImVec4), 1, fp);
  fread(&物资颜色, sizeof(ImVec4), 1, fp);
  fread(&车辆颜色, sizeof(ImVec4), 1, fp);

  fclose(fp);
}

void 绘制::OffScreen(ImDrawList *ImDraw, D4DVector Obj, float camear, ImU32 color, float Radius, float 距离)
{
  ImRect screen_rect = {0.0f, 0.0f, 真实PX, 真实PY};
  if (Obj.Z > 0 && screen_rect.Contains({Obj.X, Obj.Y}))
    return;
  auto screen_center = screen_rect.GetCenter();
  auto angle = atan2(screen_center.y - Obj.Y, screen_center.x - Obj.X);
  angle += camear > 0 ? M_PI : 0.0f;
  D2DVector arrow_center{
      screen_center.x + Radius * cosf(angle),
      screen_center.y + Radius * sinf(angle)};
  std::array<ImVec2, 4> points{
      ImVec2(-22.0f, -8.6f),
      ImVec2(0.0f, 0.0f),
      ImVec2(-22.0f, 8.6f),
      ImVec2(-18.0f, 0.0f)};
  D2DVector tpoint;
  for (auto &point : points)
  {
    auto x = point.x * 1.155f;
    auto y = point.y * 1.155f;
    point.x = arrow_center.X + x * cosf(angle) - y * sinf(angle);
    point.y = arrow_center.Y + x * sinf(angle) + y * cosf(angle);
    tpoint.X = point.x;
    tpoint.Y = point.y;
  }
  float alpha = 1.0f;
  if (camear > 0)
  {
    constexpr float nearThreshold = 200 * 200;
    ImVec2 screen_outer_diff = {
        Obj.X < 0 ? abs(Obj.X) : (Obj.X > screen_rect.Max.x ? Obj.X - screen_rect.Max.x : 0.0f),
        Obj.Y < 0 ? abs(Obj.Y) : (Obj.Y > screen_rect.Max.y ? Obj.Y - screen_rect.Max.y : 0.0f),
    };
    float distance = static_cast<float>(pow(screen_outer_diff.x, 2) + pow(screen_outer_diff.y, 2));
    alpha = camear < 0 ? 1.0f : (distance / nearThreshold);
  }
  ImColor arrowColor = color;
  arrowColor.Value.w = (alpha < 1.0f) ? alpha : 1.0f;
  ImDraw->AddTriangleFilled(points[0], points[1], points[3], arrowColor);
  ImDraw->AddTriangleFilled(points[2], points[1], points[3], arrowColor);
  ImDraw->AddQuad(points[0], points[1], points[2], points[3], ImColor(0.0f, 0.0f, 0.0f, alpha), 1.335f);

  float radius = 30.0f;
  ImColor textColor = (arrowColor.Value.x == 0.0f) ? ImColor(0.0f, 1.0f, 0.0f, alpha) : ImColor(1.0f, 0.0f, 0.0f, alpha);
  ImDraw->AddCircle({tpoint.X, tpoint.Y}, radius, textColor, 12, 2.0f);

  string tmp = to_string((int)距离) + "M";
  auto textSize = ImGui::CalcTextSize(tmp.c_str(), 0, 28);
  ImDraw->AddText(NULL, 28, {tpoint.X - (textSize.x / 2), tpoint.Y}, ImColor(1.0f, 1.0f, 1.0f, alpha), tmp.c_str());
}

void 绘制::初始化绘制(string 包名, int 真实X, int 真实Y)
{
  int pid1 = 读写.getPID(包名.c_str());
  this->pid = pid1;
  读写.初始化读写(pid1);
  绘图.初始化绘图(真实X, 真实Y);
  this->真实PX = 真实X;
  this->真实PY = 真实Y;
  骨骼 = new class 骨骼(&读写);
  if (真实Y < 真实X)
  {
    this->PX = 真实X / 2;
    this->PY = 真实Y / 2;
  }
  else
  {
    this->PX = 真实Y / 2;
    this->PY = 真实X / 2;
  }
  地址.libue4 = 读写.get_module_base((char *)"libUE4.so");
  printf("[-] 模块地址：0x%lx\n", 地址.libue4);
}
std::unordered_map<int, std::unordered_map<std::string, std::string>> itemTypeMap = {
    {1, {// 投掷物类
         {"ojGrenade_BP_C", "手雷"},
         {"ojBurn_BP_C", "燃烧弹"}}},
    {2, {// 载具类
         {"_Mountainbike_Training_C", "自行车"},
         {"Mirado", "双人跑车"},
         {"Scooter", "小绵羊"},
         {"VH_Horse", "马"},
         {"_BRDM_C", "装甲车"},
         {"VH_Motorcycle_C", "摩托车"},
         {"Snowmobile", "雪地摩托"},
         {"StationWagon", "旅行车"},
         {"BP_VH_Buggy", "蹦蹦车"},
         {"VH_Dacia_", "轿车"},
         {"VH_UAZ01_New_C", "吉普车"},
         {"PickUp_07_C", "皮卡车"},
         {"CoupeRB", "双人跑车"},
         {"_MiniBus_01_C", "迷你巴士"},
         {"_PG117_C", "快艇"},
         {"uaRail_1_C", "摩托艇"},
         {"_Motorglider_C", "滑翔机"},
         {"BP_VH_Bigfoot_C", "大脚车"},
         {"VH_ATV1_C", "四轮摩托"},
         {"Rony_01_C", "行李车"},
         {"VH_UTV_C", "越野车"},
         {"BP_VH_Tuk_1_C", "三轮车"},
         {"VH_Snowmobile_C", "雪橇车"},
         {"PG117", "船"},
         {"VH_4SportCar_C", "跑车"},
         {"BP_Excavator_C", "挖掘机"},
         {"VH_Kite_C", "风筝"},
         {"VH_Drift", "拉力赛车"},
         {"VH_Blanc_C", "SUV电车"},
         {"VH_Picobus_C", "大巴车"},
         {"VH_DumpTruck_C", "泥土车"},
         {"VH_Excavator_C", "挖掘机"},
         {"VH_LostMobile_C", "霹雳车"},
         {"VH_DesertCar_C", "沙漠越野车"}}},
    {3, {// 防具类
         {"BP_Armor_Lv3_C", "三级甲"},
         {"Bag_Lv3", "三级包"},
         {"BP_Helmet_Lv3_C", "三级头"}}},
    {4, {// 道具类
         {"BP_Pistol_RevivalFlaregun", "召回信号枪"},
         {"MilitarySupplyBoxBase_Baltic", "主题箱子"},
         {"Pistol_Flaregun", "信号枪"},
         {"_revivalAED_Pickup_C", "自救器"},
         {"BP_Neon_Coin_Pickup_C", "钱币"},
         {"BP_Pickup_Finger_C", "钩锁"},
         {"AirDropBox_C", "空投"},
         {"BP_Grenade_EmergencyCall_Weapon_Wrapper_C", "紧急呼救器"},
         {"BP_Other_Mortar_Bullet_C", "注意迫击炮"},
         {"BP_AirDropBox_SuperPeople_C", "空投"},
         {"AirDropListWrapperActor", "空投"},
         {"BP_AirdropChipBox_C", "金仓"},
         {"CG030_Market_SafeBox_C", "保险"},
         {"CG030_Market_SafeBox_2_C", "保险"},
         {"BP_AirraidActor_C", "空袭"},
         {"BP_TraceGrenade", "追踪雷"},
         {"BP_WEP_DragonBoySpear_C", "雷枪"},
         {"BuildingActor_ConcertStage_MusicGirl_BP_C", "狗窝"},
         {"perPeopleSkill", "金插"}}},
    {5, {// 盒子
         {"_PlayerDeadListWrapper_C", "盒子"}}},
    {6, {// 药品
         {"ink_Pickup_C", "饮料"},
         {"lls_Pickup_C", "止痛药"},
         {"jection_Pickup_C", "肾上腺素"},
         {"rstAidbox_Pickup_C", "医疗箱"}}},
    {7, {// 子弹
         {"_Ammo_556", "556子弹"},
         {"Ammo_9mm", "9mm子弹"},
         {"Ammo_Bolt", "箭"},
         {"Ammo_45AC", "45子弹"},
         {"Ammo_12Guage", "喷子子弹"},
         {"_Ammo_762", "762子弹"}}},
    {8, {// 枪械
         {"BP_Rifle_M762_Wrapper_C", "M762"},
         {"BP_Rifle_SCAR_Wrapper_C", "SCAR"},
         {"BP_Rifle_M416_Wrapper_C", "M416"},
         {"BP_Rifle_AKM_Wrapper_C", "AKM"},
         {"BP_Rifle_ACE32_Wrapper_C", "ACE32"},
         {"BP_Rifle_HoneyBadger_Wrapper_C", "蜜獾"},
         // 冲锋枪/轻机枪 (Submachine Guns / Light Machine Guns)
         {"BP_MachineGun_UMP9_Wrapper_C", "UMP45"},
         {"BP_MachineGun_AKS74U_Wrapper_C", "AKS"},
         {"BP_MachineGun_Vector_Wrapper_C", "Vector"},
         // 其他枪械 (Other Guns)
         {"BP_Other_Mortar_Wrapper_C", "迫击炮"},
         {"BP_Other_MG36_Wrapper_C", "MG36"},
         {"BP_Other_PKM_Wrapper_C", "PKM"},
         // 霰弹枪 (Shotguns)
         {"BP_ShotGun_S12K_Wrapper_C", "S12K"},
         {"BP_ShotGun_DP12_Wrapper_C", "DBS"},
         {"BP_ShotGun_SPAS_Wrapper_C", "SPAS"},
         {"BP_ShotGun_AA12_Wrapper_C", "AA12"},
         // 狙击枪 (Sniper Rifles)
         {"BP_Sniper_SVD_Wrapper_C", "SVD"},
         {"BP_Sniper_SKS_Wrapper_C", "SKS"},
         {"BP_Rifle_M417_Wrapper_C", "M417"},
         {"BP_Other_HuntingBow_Wrapper_C", "爆炸弓"},
         {"BP_Sniper_Mini14_Wrapper_C", "Mini14"}}},
    {9, {// 配件
         {"_DJ_Large_EQ_Pickup_C", "步枪快扩"},
         {"DJ_Sniper_EQ", "狙击快扩"},
         {"DJ_Mid_EQ", "冲锋快扩"},
         {"BР_DJ_ShotGun_Pickup_C", "散弹快速"},
         {"BP_WB_LightGrip_Pickup_C", "轻型"},
         {"BP_WB_Lasersight_Pickup_C", "激光"},
         {"BP_WB_Angled_Pickup_C", "直角"},
         {"QK_Large_Suppressor", "步消"},
         {"QK_Sniper_Suppressor", "狙消"},
         {"QK_Mid_Suppressor", "冲消"},
         {"_MZJ_8X_Pickup_C", "8倍"},
         {"_MZJ_6X_Pickup_C", "6倍"}}},
    {10, {// 地铁宝箱
          {"EscapeBox_SupplyBox_", "物资箱"},
          {"EscapeBoxHight_SupplyBox_", "物资箱"}}}};
bool isPartialMatchedType(int type, const std::string &ClassName, std::string &matchedName)
{
  auto it = itemTypeMap.find(type);
  if (it != itemTypeMap.end())
  {
    auto &subMap = it->second;
    for (const auto &entry : subMap)
    {
      if (ClassName.find(entry.first) != std::string::npos)
      {
        matchedName = entry.second;
        return true;
      }
    }
  }
  return false;
}
int 绘制::findminat()
{
  float DistanceMin = 450.0f;
  float min = 自瞄.自瞄范围;
  int minAt = 999;

  // 如果正在开火且已有锁定目标,保持锁定
  if (自身数据.开火 == 1 && 自瞄.瞄准目标 != -1 && 自瞄.瞄准目标 < 自瞄.瞄准总数量)
  {
    // 只要目标还在自瞄范围内就继续锁定
    if (自瞄函数[自瞄.瞄准目标].准心距离 <= 自瞄.自瞄范围 && 自瞄函数[自瞄.瞄准目标].准心距离 != 0)
    {
      return 自瞄.瞄准目标;
    }
  }

  // 没有开火或目标无效,按瞄准优先级重新选择
  for (int i = 0; i < 自瞄.瞄准总数量; i++)
  {
    switch ((int)自瞄.瞄准优先)
    {
    case 0:
      if (自瞄函数[i].准心距离 < min && 自瞄函数[i].准心距离 != 0)
      {
        min = 自瞄函数[i].准心距离;
        minAt = i;
      }
      break;
    case 1:
      if (自瞄函数[i].准心距离 < 自瞄.自瞄范围)
      {
        if (自瞄函数[i].距离 < DistanceMin)
        {
          DistanceMin = 自瞄函数[i].距离;
          minAt = i;
        }
      }
      break;
    }
  }

  if (minAt == 999)
  {
    自瞄.瞄准目标 = -1;
    return -1;
  }

  自瞄.瞄准目标 = minAt;
  return minAt;
}

void 绘制::GetTouch()
{
  std::thread *触摸位置线程 = new std::thread([&]
                                              {
    for(;;)
    {
      usleep(1000000 / 120);
      ImGuiIO& iooi = ImGui::GetIO();
      if (自瞄.触摸位置 && iooi.MouseDown[0] && iooi.MousePos.x <= 自瞄.触摸范围X + 自瞄.触摸范围 && iooi.MousePos.y <= displayInfo.height - 自瞄.触摸范围Y + 自瞄.触摸范围 && iooi.MousePos.x >= 自瞄.触摸范围X - 自瞄.触摸范围 && iooi.MousePos.y >= displayInfo.height - 自瞄.触摸范围Y - 自瞄.触摸范围)
      {
        usleep(30000);
        if (自瞄.触摸位置 && iooi.MouseDown[0] && iooi.MousePos.x <= 自瞄.触摸范围X + 自瞄.触摸范围 && iooi.MousePos.y <= displayInfo.height - 自瞄.触摸范围Y + 自瞄.触摸范围 && iooi.MousePos.x >= 自瞄.触摸范围X - 自瞄.触摸范围 && iooi.MousePos.y >= displayInfo.height - 自瞄.触摸范围Y - 自瞄.触摸范围)
        {
          while (自瞄.触摸位置 && iooi.MouseDown[0] && iooi.MousePos.x <= 自瞄.触摸范围X + 自瞄.触摸范围 && iooi.MousePos.y <= displayInfo.height - 自瞄.触摸范围Y + 自瞄.触摸范围 && iooi.MousePos.x >= 自瞄.触摸范围X - 自瞄.触摸范围 && iooi.MousePos.y >= displayInfo.height - 自瞄.触摸范围Y - 自瞄.触摸范围)
          {
            自瞄.触摸范围X = iooi.MousePos.x;
            自瞄.触摸范围Y = displayInfo.height - iooi.MousePos.y;
            usleep(500);
          }
        }
      }
    } });
  触摸位置线程->detach();
}

bool 绘制::自瞄触发(float 距离)
{
  if (自瞄.喷子自瞄)
  {
    if (自身数据.手持 == 104003 or 自身数据.手持 == 104005 or 自身数据.手持 == 104100 or 自身数据.手持 == 104004)
    {
      if (距离 < 自瞄.喷子距离限制)
      {
        if (自瞄.喷子自瞄条件 == 0 or (自瞄.喷子自瞄条件 == 1 && 自身数据.开镜 != 0))
        {
          return true;
        }
      }
    }
  }

  if (自瞄函数[自瞄.瞄准目标].距离 > 自瞄.自瞄距离限制)
  {
    return false;
  }
  if (自瞄函数[自瞄.瞄准目标].距离 > 自瞄.腰射距离限制 && 自身数据.开火 != 0 && 自身数据.Fov > 75)
  {
    return false;
  }
  if (自瞄.狙击自瞄)
  {
    if (自身数据.手持 == 103011 or 自身数据.手持 == 103001 or 自身数据.手持 == 103003 or 自身数据.手持 == 103015 or 自身数据.手持 == 103012 or 自身数据.手持 == 103002)
    {
      if (自身数据.开镜 != 0)
      {
        return true;
      }
    }
  }
  switch ((int)自瞄.自瞄条件)
  {
  case 0:
    if (自身数据.开火 != 1)
    {
      return false;
    }
    break;
  case 1:
    if (自身数据.开镜 != 1)
    {
      return false;
    }
    break;
  case 2:
    if (自身数据.开火 == 0 && 自身数据.开镜 == 0)
    {
      return false;
    }
    break;
  }
  return true;
}
void 绘制::自瞄主线程()
{
  std::thread *自瞄线程 = new std::thread([&]
                                          {
    bool isDown = false;    
    float halfSize = 自瞄.触摸范围 / 2;
    double RandomnumberX = 自瞄.触摸范围Y,RandomnumberY = 自瞄.触摸范围X;
    double tx = 自瞄.触摸范围Y, ty = 自瞄.触摸范围X;
    
    if (自瞄.随机触摸点) {
      RandomnumberY = 自瞄.触摸范围X - halfSize + (rand() % (int)自瞄.触摸范围);
      RandomnumberX = 自瞄.触摸范围Y - halfSize + (rand() % (int)自瞄.触摸范围);
      tx = RandomnumberX, ty = RandomnumberY;    
    }
    
    double ScreenX,ScreenY;
    if(displayInfo.orientation == 1 || displayInfo.orientation == 3){
       ScreenX = displayInfo.height; ScreenY = displayInfo.width;
    }else {
       ScreenX = displayInfo.width; ScreenY = displayInfo.height;
    }
    //double ScreenX = displayInfo.width, ScreenY = displayInfo.height;
    double ScrXH = ScreenX / 2.0f;
    double ScrYH = ScreenY / 2.0f;
    static float TargetX = 0;
    static float TargetY = 0;
    D3DVector obj;
    float NowCoor[3];
    float zm_x,zm_y;
    
    int 目标血量 = 100;
    string 目标名字;
    bool 自瞄测试 = false;
    int 数率 = 0;
    
    timer AimFPS;
    AimFPS.SetFps(120);
    AimFPS.AotuFPS_init();
    AimFPS.setAffinity();
    
    while (1)
    {
      if (!自瞄.随机触摸点) {
        RandomnumberX = 自瞄.触摸范围Y,RandomnumberY = 自瞄.触摸范围X;
      }
      if (!自瞄.初始化)
      {
        if (isDown == true)
        {
          tx = 自瞄.触摸范围Y, ty = 自瞄.触摸范围X;
          if (自瞄.随机触摸点) {
            RandomnumberY = 自瞄.触摸范围X - halfSize + (rand() % (int)自瞄.触摸范围);
            RandomnumberX = 自瞄.触摸范围Y - halfSize + (rand() % (int)自瞄.触摸范围);
            tx = RandomnumberX, ty = RandomnumberY;
          }
          // 恢复变量
          Touch_Up();
          // 抬起
          isDown = false;
        }
        usleep(自瞄.自瞄速度 * 500);
        continue;
      }
      findminat();
      if (自瞄.瞄准目标 == -1)
      {
        if (isDown == true)
        {
          tx = 自瞄.触摸范围Y, ty = 自瞄.触摸范围X;
          if (自瞄.随机触摸点) {
            RandomnumberY = 自瞄.触摸范围X - halfSize + (rand() % (int)自瞄.触摸范围);
            RandomnumberX = 自瞄.触摸范围Y - halfSize + (rand() % (int)自瞄.触摸范围);
            tx = RandomnumberX, ty = RandomnumberY;
          }
          // 恢复变量
          Touch_Up();
          // 抬起
          isDown = false;
        }
        usleep(自瞄.自瞄速度 * 500);
        continue;
      }
      float ToReticleDistance = 自瞄函数[自瞄.瞄准目标].准心距离;
      float BulletFlightTime = 自瞄函数[自瞄.瞄准目标].距离 / 自身数据.子弹速度;
      float FlyTime;
      float 预判力度a = 自瞄.预判力度;
      if(对象信息.敌人信息.乘坐载具) {
        预判力度a = 预判度.扫车;
      }else{
        预判力度a = 自瞄.预判力度;
      }
      if (自瞄函数[自瞄.瞄准目标].距离 >= 40) {
        FlyTime = 自瞄函数[自瞄.瞄准目标].距离 / (自身数据.子弹速度 * 0.01f) * 预判力度a;
      }else {
        FlyTime = 自瞄函数[自瞄.瞄准目标].距离 / (自身数据.子弹速度 * 0.0055f) * 预判力度a;
      }
      float DropM = 540.0f * BulletFlightTime * BulletFlightTime;
      float 压枪力度 = 自瞄.压枪力度;
      if (自身数据.人物高度 == 120.0f) {
        // 使用趴下状态的压枪力度
        float pressureValue = 自瞄.GetPressureValue(自身数据.手持, true, 自身数据.Fov);
        压枪力度 = 自瞄.趴下位置调节 * pressureValue;
      }else{
        // 使用站立状态的压枪力度
        float pressureValue = 自瞄.GetPressureValue(自身数据.手持, false, 自身数据.Fov);
        压枪力度 = 自瞄.压枪力度 * pressureValue;
      }
      NowCoor[0] = 自瞄函数[自瞄.瞄准目标].瞄准坐标.X;
      NowCoor[1] = 自瞄函数[自瞄.瞄准目标].瞄准坐标.Y;
      NowCoor[2] = 自瞄函数[自瞄.瞄准目标].瞄准坐标.Z;
      obj.X = NowCoor[0] + (自瞄函数[自瞄.瞄准目标].人物向量.X * FlyTime);
      obj.Y = NowCoor[1] + (自瞄函数[自瞄.瞄准目标].人物向量.Y * FlyTime);
      obj.Z = NowCoor[2] + (自瞄函数[自瞄.瞄准目标].人物向量.Z * FlyTime) + DropM;
      if (自身数据.开火 == 1) {
        obj.Z -= 自瞄函数[自瞄.瞄准目标].距离 * 压枪力度;
      }
      D2DVector vpvp = 计算.计算屏幕坐标2(自身数据.矩阵, obj, PX, PY);
      float AimDs = sqrt(pow(PX - vpvp.X, 2) + pow(PY - vpvp.Y, 2));
      if(自瞄.动态自瞄 && (自身数据.开火==1 || 自身数据.开镜 == 1))
      {
        自瞄.动态范围 = AimDs;
      }else{
        自瞄.动态范围 = 自瞄.自瞄范围;
      }      
      zm_y = vpvp.X;
      zm_x = ScreenX - vpvp.Y;
      if (zm_x <= 0 || zm_x >= ScreenX || zm_y <= 0 || zm_y >= ScreenY)
      {
        if (isDown == true)
        {
          tx = 自瞄.触摸范围Y, ty = 自瞄.触摸范围X;
          if (自瞄.随机触摸点) {
            RandomnumberY = 自瞄.触摸范围X - halfSize + (rand() % (int)自瞄.触摸范围);
            RandomnumberX = 自瞄.触摸范围Y - halfSize + (rand() % (int)自瞄.触摸范围);
            tx = RandomnumberX, ty = RandomnumberY;
          }
          // 恢复变量
          Touch_Up();
          // 抬起
          isDown = false;
        }
        usleep(自瞄.自瞄速度 * 500);
        continue;
      }
      if (ToReticleDistance <= 自瞄.自瞄范围)
      {
        if (!自瞄触发(自瞄函数[自瞄.瞄准目标].距离))
        {
          if (isDown == true)
          {
            tx = 自瞄.触摸范围Y, ty = 自瞄.触摸范围X;
            if (自瞄.随机触摸点) {
              RandomnumberY = 自瞄.触摸范围X - halfSize + (rand() % (int)自瞄.触摸范围);
              RandomnumberX = 自瞄.触摸范围Y - halfSize + (rand() % (int)自瞄.触摸范围);
              tx = RandomnumberX, ty = RandomnumberY;
            }
            // 恢复变量
            Touch_Up();
            isDown = false;
          }
          usleep(自瞄.自瞄速度 * 500);
          continue;
        }

        if (自瞄.框内自瞄) {
          if ((自瞄函数[自瞄.瞄准目标].人物向量.X == 0 and AimDs > 35) or (自瞄函数[自瞄.瞄准目标].人物向量.X != 0 and AimDs > 65))
          {
            if (isDown == true)
            {
              tx = 自瞄.触摸范围Y, ty = 自瞄.触摸范围X;
              if (自瞄.随机触摸点) {
                RandomnumberY = 自瞄.触摸范围X - halfSize + (rand() % (int)自瞄.触摸范围);
                RandomnumberX = 自瞄.触摸范围Y - halfSize + (rand() % (int)自瞄.触摸范围);
                tx = RandomnumberX, ty = RandomnumberY;
              }
              // 恢复变量
              Touch_Up();
              isDown = false;
            }
            usleep(自瞄.自瞄速度 * 500);
            continue;
          }
        }
        
        if (自瞄.掉血自瞄) {
          if (数率 > 0) {
            数率++;
            if (数率 == (int)自瞄.掉血自瞄数率) {
              数率 = 0;
            }
          }else {
            if (自瞄函数[自瞄.瞄准目标].名字 == 目标名字) {
              if (自瞄函数[自瞄.瞄准目标].血量 >= 目标血量) {
                目标血量 = 自瞄函数[自瞄.瞄准目标].血量;
                continue;
              }else {
                数率++;
                目标血量 = 自瞄函数[自瞄.瞄准目标].血量;
              }
            }else {
              目标名字 = 自瞄函数[自瞄.瞄准目标].名字;
              目标血量 = 自瞄函数[自瞄.瞄准目标].血量;
              continue;
            }
          }
        }
        
        if (isDown == false)
        {
          if (自瞄.充电口方向 == 0)
            Touch_Down((int)tx, (int)ty);
          else
            Touch_Down(displayInfo.height - (int)tx, displayInfo.width - (int)ty);
          isDown = true;
        }
        
        float Acc = getScopeAcc((int)(90 / 自身数据.Fov));        
        if (zm_x > ScrXH) {
          TargetX = -(ScrXH - zm_x) / 自瞄.自瞄速度 * Acc;
          if (TargetX + ScrXH > ScrXH * 2) {
            TargetX = 0;
          }
        }
        else if (zm_x < ScrXH) {
          TargetX = (zm_x - ScrXH) / 自瞄.自瞄速度 * Acc;
          if (TargetX + ScrXH < 0) {
            TargetX = 0;
          }
        }
        
        if (zm_y > ScrYH) {
          TargetY = -(ScrYH - zm_y) / 自瞄.自瞄速度 * Acc;
          if (TargetY + ScrYH > ScrYH * 2) {
            TargetY = 0;
          }
        }
        else if (zm_y < ScrYH) {
          TargetY = (zm_y - ScrYH) / 自瞄.自瞄速度 * Acc;
          if (TargetY + ScrYH < 0) {
            TargetY = 0;
          }
        }
        if (TargetY >= 35 || TargetX >= 35 || TargetY <= -35 || TargetX <= -35)
        {
          if (isDown == true)
          {
            tx = 自瞄.触摸范围Y, ty = 自瞄.触摸范围X;
            if (自瞄.随机触摸点) {
              RandomnumberY = 自瞄.触摸范围X - halfSize + (rand() % (int)自瞄.触摸范围);
              RandomnumberX = 自瞄.触摸范围Y - halfSize + (rand() % (int)自瞄.触摸范围);
              tx = RandomnumberX, ty = RandomnumberY;
            }
            // 恢复变量
            Touch_Up();
            isDown = false;
          }
          usleep(自瞄.自瞄速度 * 500);
          continue;
        }
        
        tx += TargetX;
        ty += TargetY;
        if (tx >= RandomnumberX + 自瞄.触摸范围 || tx <= RandomnumberX - 自瞄.触摸范围
        || ty >= RandomnumberY + 自瞄.触摸范围 || ty <= RandomnumberY - 自瞄.触摸范围)
        {
          tx = 自瞄.触摸范围Y, ty = 自瞄.触摸范围X;
          if (自瞄.随机触摸点) {
            RandomnumberY = 自瞄.触摸范围X - halfSize + (rand() % (int)自瞄.触摸范围);
            RandomnumberX = 自瞄.触摸范围Y - halfSize + (rand() % (int)自瞄.触摸范围);
            tx = RandomnumberX, ty = RandomnumberY;
          }
          // 恢复变量
          Touch_Up();
          // 抬起
          
          if (自瞄.充电口方向 == 0)
            Touch_Down((int)tx, (int)ty);
          else
            Touch_Down(displayInfo.height - (int)tx, displayInfo.width - (int)ty);         
          
          isDown = true;
        }
        if (自瞄.充电口方向 == 0)
          Touch_Move((int)tx, (int)ty);
        else
          Touch_Move(displayInfo.height - (int)tx, displayInfo.width - (int)ty);
        isDown = true;
      }
      else
      {
        if (isDown == true)
        {
          tx = 自瞄.触摸范围Y, ty = 自瞄.触摸范围X;
          if (自瞄.随机触摸点) {
            RandomnumberY = 自瞄.触摸范围X - halfSize + (rand() % (int)自瞄.触摸范围);
            RandomnumberX = 自瞄.触摸范围Y - halfSize + (rand() % (int)自瞄.触摸范围);
            tx = RandomnumberX, ty = RandomnumberY;
          }
          // 恢复变量
          Touch_Up();
          // 抬起
          isDown = false;
        }
      }
      usleep(自瞄.自瞄速度 * 500);
      AimFPS.SetFps(按钮.当前帧率);
      AimFPS.AotuFPS();
    } });
  自瞄线程->detach();
}

void 绘制::更新地址数据()
{
  地址.世界地址 = 读写.getPtr64(读写.getPtr64(地址.libue4 + 0x12161358) + 0x90);
  地址.自身地址 = 读写.getPtr64(读写.getPtr64(读写.getPtr64(读写.getPtr64(读写.getPtr64(地址.libue4 + 0x12161358) + 0x98) + 0x88) + 0x30) + 0x32a8);
  地址.矩阵地址 = 读写.getPtr64(读写.getPtr64(地址.libue4 + 0x12132D60) + 0x20) + 0x270;
  // 地址.矩阵地址_Tol = 读写.getPtr64(读写.getPtr64(地址.libue4 + 0x112313B0) + 0x98) + 0x750;
  读写.readv(读写.getPtr64(地址.自身地址 + 0x278) + 0x200, &自身数据.坐标, sizeof(自身数据.坐标)); // 更新坐标
  地址.数组地址 = 读写.getPtr64(地址.世界地址 + 0xA0);
  世界数量 = 读写.getDword(地址.世界地址 + 0xA8);
  if (解密数组)
  {
    地址.数组地址 = 解密数组;
    世界数量 = 读写.getDword(地址.世界地址 + 0xB8);
  }
  地址.类地址 = 读写.getPtr64(地址.libue4 + 0x11EFDCA0);
  读写.readv(地址.矩阵地址_Tol - 0x10, &自身数据.坐标, sizeof(自身数据.坐标)); // 更新坐标
  读写.readv(地址.矩阵地址, &自身数据.矩阵, sizeof(自身数据.矩阵));            // 更新矩阵信息
  自身数据.自身队伍 = 读写.getDword(地址.自身地址 + 0xac0);
  自身数据.自身状态 = 读写.getDword(读写.getPtr64(地址.自身地址 + 0x1538));
  自身数据.开镜 = 读写.getDword(地址.自身地址 + 0x1668) == 1;
  自身数据.开火 = 读写.getDword(地址.自身地址 + 0x23e0);
  自身数据.手持id = 读写.getDword(读写.getPtr64(地址.自身地址 + 0xfe8) + 0xbd0);
  自身数据.手持 = heldconversion(自身数据.手持id);
  自身数据.Fov = 读写.getFloat(读写.getPtr64(读写.getPtr64(地址.自身地址 + 0x5588) + 0x608) + 0x630);
  自身数据.子弹速度 = 读写.getFloat(读写.getPtr64(读写.getPtr64(地址.自身地址 + 0x33f0 + 0x20) + 0x19d0) + 0x13f4);
  自身数据.准星Y = 读写.getFloat(读写.getPtr64(地址.自身地址 + 0x5588) + 0x58c) - 90;
  自身数据.人物高度 = 读写.getFloat(地址.自身地址 + 0x1b4);
}
ImColor 绘制::floatArrToImColor(float arr[4])
{
  return ImColor(arr[0] * 255, arr[1] * 255, arr[2] * 255, arr[3] * 255);
}

void 绘制::更新对象数据()
{
  if (自瞄.触摸位置)
    绘图.绘制自瞄触摸范围(自瞄.触摸范围, 自瞄.触摸范围X, 自瞄.触摸范围Y);
  if (!自瞄.动态自瞄)
  {
    自瞄.动态范围 = 自瞄.自瞄范围;
  }
  if (自瞄.初始化 && !自瞄.隐藏自瞄圈)
  {
    ImGui::GetForegroundDrawList()->AddCircle({PX, PY}, 自瞄.动态范围, ImColor(255, 192, 203, 255), 0, 1.0f);
  }
  if (按钮.雷达)
  {
    绘图.RenderRadarScan(ImGui::GetForegroundDrawList(), ImVec2(按钮.雷达X, 按钮.雷达Y), 150.0f, 100, 按钮.rotationAngle, 150.0f);
  }

  int 绘制人机 = 0, 绘制真人 = 0;
  被瞄准对象数量 = 0;
  自瞄.瞄准对象数量 = 0;
  for (int a = 0; a < 世界数量; a++)
  {
    对象地址.敌人地址 = 读写.getPtr64(地址.数组地址 + a * 8);
    读写.readv(读写.getPtr64(对象地址.敌人地址 + 0x278) + 0x200, &对象信息.敌人信息.坐标, sizeof(对象信息.敌人信息.坐标)); // 更新坐标
    对象信息.敌人信息.距离 = 计算.计算距离(自身数据.坐标, 对象信息.敌人信息.坐标);                                         // 距离

    float camear_r = 自身数据.矩阵[3] * 对象信息.敌人信息.坐标.X + 自身数据.矩阵[7] * 对象信息.敌人信息.坐标.Y + 自身数据.矩阵[11] * 对象信息.敌人信息.坐标.Z + 自身数据.矩阵[15];
    float r_x = PX + (自身数据.矩阵[0] * 对象信息.敌人信息.坐标.X + 自身数据.矩阵[4] * 对象信息.敌人信息.坐标.Y + 自身数据.矩阵[8] * 对象信息.敌人信息.坐标.Z + 自身数据.矩阵[12]) / camear_r * PX;
    float r_y = PY - (自身数据.矩阵[1] * 对象信息.敌人信息.坐标.X + 自身数据.矩阵[5] * 对象信息.敌人信息.坐标.Y + 自身数据.矩阵[9] * (对象信息.敌人信息.坐标.Z - 5) + 自身数据.矩阵[13]) / camear_r * PY;
    float r_z = PY - (自身数据.矩阵[1] * 对象信息.敌人信息.坐标.X + 自身数据.矩阵[5] * 对象信息.敌人信息.坐标.Y + 自身数据.矩阵[9] * (对象信息.敌人信息.坐标.Z + 205) + 自身数据.矩阵[13]) / camear_r * PY;
    D4DVector t_屏幕坐标 = {r_x - (r_y - r_z) / 4, r_y, (r_y - r_z) / 2, r_y - r_z};

    float X = r_x - (r_y - r_z) / 4;
    float Y = r_y;
    float W = (r_y - r_z) / 2;
    float MIDDLE = X + W / 2;
    float BOTTOM = Y + W;
    float TOP = Y - W;

    char 计算地址[256] = "我是帅哥";
    sprintf(计算地址, "%lx", 对象地址.敌人地址);

    char ClassName[64] = "";
    char 对象信息_max[200] = "";
    int ClassID = 读写.getPtr64(对象地址.敌人地址 + 24);
    long int FNameEntry;

    if (t_屏幕坐标.W > 0)
    { // 车辆物资区域
      FNameEntry = 读写.getPtr64(读写.getPtr64(地址.类地址 + (ClassID / 0x4000) * 0x8) + (ClassID % 0x4000) * 0x8);
      读写.readv(FNameEntry + 0xC, ClassName, 64);
      if (按钮.Debug)
      {
        if (按钮.Debug模式 == 0)
        {
          auto textSize = ImGui::CalcTextSize(ClassName, 0, 30);
          ImGui::GetForegroundDrawList()->AddText(NULL, 30, {r_x - (textSize.x / 2), r_y}, ImColor(255, 255, 255, 255), ClassName);
        }
        else
        {
          auto textSize = ImGui::CalcTextSize(计算地址, 0, 30);
          ImGui::GetForegroundDrawList()->AddText(NULL, 30, {r_x - (textSize.x / 2), r_y}, ImColor(255, 255, 255, 255), 计算地址);
        }
      }
      std::string name;        // 声明name变量
      std::string matchedName; // 声明matchedName变量
      struct ItemConfig
      {
        std::string name;  // 物资名称
        ImColor color;     // 物资颜色
        float minDistance; // 最小距离限制
        float maxDistance; // 最大距离限制
        bool valid;        // 是否满足条件
      };
      // 配置物资类型及其属性
      std::vector<ItemConfig> itemConfigs = {
          {"投掷", ImColor(ImVec4(1.0f, 0.0f, 0.0f, 0.95f)), 0, 100, 按钮.绘制投掷 && isPartialMatchedType(1, ClassName, matchedName)},
          {"载具", ImColor(ImVec4(0.0f, 1.0f, 0.0f, 0.95f)), 10, 600, 按钮.绘制载具 && isPartialMatchedType(2, ClassName, matchedName)},
          {"防具", ImColor(ImVec4(0.118f, 0.565f, 1.0f, 0.95f)), 0, 100, 按钮.绘制防具 && isPartialMatchedType(3, ClassName, matchedName)},
          {"道具", ImColor(ImVec4(1.0f, 1.0f, 0.0f, 0.95f)), 0, 1700, 按钮.绘制道具 && isPartialMatchedType(4, ClassName, matchedName)},
          {"盒子", ImColor(ImVec4(0.647f, 0.165f, 0.165f, 0.95f)), 0, 100, 按钮.绘制盒子 && isPartialMatchedType(5, ClassName, matchedName)},
          {"药品", ImColor(ImVec4(0.5f, 0.0f, 0.5f, 0.95f)), 0, 100, 按钮.绘制药品 && isPartialMatchedType(6, ClassName, matchedName)},
          {"子弹", ImColor(ImVec4(0.914f, 0.184f, 0.024f, 0.95f)), 0, 100, 按钮.绘制子弹 && isPartialMatchedType(7, ClassName, matchedName)},
          {"武器", ImColor(ImVec4(0.914f, 0.184f, 0.024f, 0.95f)), 0, 100, 按钮.绘制武器 && isPartialMatchedType(8, ClassName, matchedName)},
          {"配件", ImColor(ImVec4(1.0f, 0.647f, 0.0f, 0.95f)), 0, 100, 按钮.绘制配件 && isPartialMatchedType(9, ClassName, matchedName)},
          {"地铁", ImColor(ImVec4(1.0f, 0.0f, 0.0f, 0.95f)), 0, 600, 按钮.绘制地铁 && isPartialMatchedType(10, ClassName, matchedName)}};
      // 遍历所有物资类型并处理
      for (const auto &config : itemConfigs)
      {
        // 检查是否有效并在距离限制内
        if (config.valid && 对象信息.敌人信息.距离 >= config.minDistance && 对象信息.敌人信息.距离 <= config.maxDistance)
        {
          name = matchedName; // 使用matchedName作为基础名称
          name += "[";
          name += std::to_string((int)对象信息.敌人信息.距离);
          name += "米]";
          auto textSize = ImGui::CalcTextSize(name.c_str(), 0, 30);
          ImGui::GetForegroundDrawList()->AddText(NULL, 30, {r_x - (textSize.x / 2), r_y}, 绘制::floatArrToImColor(绘制::物资颜色), name.c_str());
          break; // 匹配到一个物资后立即退出循环
        }
      }
    }

    if (strstr(ClassName, "BPPawn_Escape_Raven") != 0 or strstr(ClassName, "BPPawn_Escape_UAV_C") != 0)
    {
      continue;
    }
    if (读写.getDword(地址.自身地址) == 0 && 对象信息.敌人信息.距离 <= 5)
    {
      continue;
    }

    bool isboss = 骨骼->isBoss(*ClassName);

    if (读写.getFloat(对象地址.敌人地址 + 0x3518) == 479.5 || strstr(ClassName, "BPPawn_Escape_") != 0 || isboss)
    {
      // printf("%lx\n",对象地址.敌人地址);
      D4DVector 屏外预警坐标(r_x, r_y, r_y - r_z, (r_y - r_z) / 2);
      对象信息.敌人信息.队伍 = 读写.getDword(对象地址.敌人地址 + 0xac0);
      对象信息.敌人信息.isboot = (对象信息.敌人信息.队伍 == -1) ? 1 : 读写.getDword(对象地址.敌人地址 + 0xadc); // 人机
      if (按钮.忽略人机 && 对象信息.敌人信息.isboot == 1)
      {
        continue;
      }
      对象信息.敌人信息.状态 = 读写.getDword(读写.getPtr64(对象地址.敌人地址 + 0x1538));
      对象信息.敌人信息.雷达 = 计算.rotateCoord(自身数据.准星Y, (自身数据.坐标.X - 对象信息.敌人信息.坐标.X) / 200, (自身数据.坐标.Y - 对象信息.敌人信息.坐标.Y) / 200);

      读写.readv(对象地址.敌人地址 + 0xfdc, &对象信息.敌人信息.向量, sizeof(对象信息.敌人信息.向量)); // 更新向量

      对象信息.敌人信息.Rotator = 读写.getFloat(对象地址.敌人地址 + 0x1A8);

      对象信息.敌人信息.当前血量 = 读写.getFloat(对象地址.敌人地址 + 0xed8); // 血量
      对象信息.敌人信息.最大血量 = 读写.getFloat(对象地址.敌人地址 + 0xee0); // 最大血量
      对象信息.敌人信息.乘坐载具 = 读写.getDword(对象地址.敌人地址 + 0x1160) != 0;
      对象信息.敌人信息.手持 = 读写.getDword(读写.getPtr64(对象地址.敌人地址 + 0xfe8) + 0xbd0);
      对象信息.敌人信息.子弹数量 = 读写.getDword(读写.getPtr64(对象地址.敌人地址 + 0xf80) + 0x18b0);
      对象信息.敌人信息.子弹最大数量 = 读写.getDword(读写.getPtr64(对象地址.敌人地址 + 0xf80) + 0x18b4);
      long int MeshOffset = 读写.getPtr64(对象地址.敌人地址 + 0x600);
      int Bonecount = 读写.getPtr64(MeshOffset + 0x7F8 + 8);
      骨骼->更新骨骼数据(MeshOffset + 0x1F0, 读写.getPtr64(MeshOffset + 0x7F8) + 0x30, 对象信息.敌人信息.骨骼坐标, Bonecount, 对象信息.敌人信息.队伍, ClassName);
      char temp[64];
      读写.getUTF8(temp, 读写.getPtr64(对象地址.敌人地址 + 0xa40));
      对象信息.敌人信息.名字 = temp;
      // 手雷倒计时计算
      if (对象信息.敌人信息.队伍 == 自身数据.自身队伍)
        continue;
      bool LineOfSightTo1 = true;
      if (按钮.雷达)
      {
        if (对象信息.敌人信息.距离 <= 300)
        {
          if (对象信息.敌人信息.isboot == 1)
          {
            ImGui::GetForegroundDrawList()->AddCircleFilled({按钮.雷达X + 对象信息.敌人信息.雷达.X, 按钮.雷达Y + 对象信息.敌人信息.雷达.Y}, {4.5}, ImColor(255, 255, 255));
          }
          else
          {
            ImGui::GetForegroundDrawList()->AddCircleFilled({按钮.雷达X + 对象信息.敌人信息.雷达.X, 按钮.雷达Y + 对象信息.敌人信息.雷达.Y}, {4.5}, ImColor(255, 0, 0));
          }
        }
      }
      if (对象信息.敌人信息.状态 == 1048592 || 对象信息.敌人信息.状态 == 1048576)
        continue;
      if (对象信息.敌人信息.isboot == 1)
      {
        绘制人机++;
      }
      else
      {
        绘制真人++;
      }
      // 下面开始绘制
      骨骼数据 t_骨骼数据 = 计算.计算骨骼(自身数据.矩阵, 对象信息.敌人信息.骨骼坐标, PX, PY);
      绘图.初始化坐标(t_屏幕坐标, t_骨骼数据);
      if (t_屏幕坐标.W >= 0)
      {
        if (自瞄.倒地不瞄 && 对象信息.敌人信息.当前血量 <= 0)
        {
        }
        else if (自瞄.人机不瞄 && 对象信息.敌人信息.isboot == 1)
        {
        }
        else
        {
          自瞄函数[自瞄.瞄准对象数量].距离 = 对象信息.敌人信息.距离;
          自瞄函数[自瞄.瞄准对象数量].人物向量 = 对象信息.敌人信息.向量;
          自瞄函数[自瞄.瞄准对象数量].血量 = 对象信息.敌人信息.当前血量;
          自瞄函数[自瞄.瞄准对象数量].Bone = 读写.getPtr64(MeshOffset + 0x7F8) + 0x30;
          自瞄函数[自瞄.瞄准对象数量].Human = MeshOffset + 0x1F0;
          自瞄函数[自瞄.瞄准对象数量].名字 = 对象信息.敌人信息.名字;
          自瞄函数[自瞄.瞄准对象数量].阵营 = 对象信息.敌人信息.队伍;
          memcpy(自瞄函数[自瞄.瞄准对象数量].骨骼坐标, 对象信息.敌人信息.骨骼坐标, sizeof(对象信息.敌人信息.骨骼坐标));

          if (自瞄.瞄准部位 == 0)
          {
            自瞄函数[自瞄.瞄准对象数量].瞄准坐标 = 对象信息.敌人信息.骨骼坐标[0];
            自瞄函数[自瞄.瞄准对象数量].准心距离 = sqrt(pow(PX - t_骨骼数据.Head.X, 2) + pow(PY - t_骨骼数据.Head.Y, 2));
            自瞄函数[自瞄.瞄准对象数量].对象骨骼 = t_骨骼数据.Head;
          }
          else if (自瞄.瞄准部位 == 1)
          {
            自瞄函数[自瞄.瞄准对象数量].瞄准坐标 = 对象信息.敌人信息.骨骼坐标[1];
            自瞄函数[自瞄.瞄准对象数量].准心距离 = sqrt(pow(PX - t_骨骼数据.Chest.X, 2) + pow(PY - t_骨骼数据.Chest.Y, 2));
            自瞄函数[自瞄.瞄准对象数量].对象骨骼 = t_骨骼数据.Chest;
          }
          else if (自瞄.瞄准部位 == 2)
          {
            自瞄函数[自瞄.瞄准对象数量].瞄准坐标 = 对象信息.敌人信息.骨骼坐标[2];
            自瞄函数[自瞄.瞄准对象数量].准心距离 = sqrt(pow(PX - t_骨骼数据.Pelvis.X, 2) + pow(PY - t_骨骼数据.Pelvis.Y, 2));
            自瞄函数[自瞄.瞄准对象数量].对象骨骼 = t_骨骼数据.Pelvis;
          }
          自瞄.瞄准对象数量++;
        }
      }

      if (按钮.背敌预警)
      {

        char 背敌距离[250];
        sprintf(背敌距离, "%dM", ((int)对象信息.敌人信息.距离));
        auto textSize = ImGui::CalcTextSize(背敌距离, 0, 25);
        if (X + W / 2 < 0)
        {
          ImGui::GetForegroundDrawList()->AddCircleFilled({0, t_骨骼数据.Head.Y}, 60, ImColor(255, 255, 255, 255));
          ImGui::GetForegroundDrawList()->AddText(NULL, 25, {5, t_骨骼数据.Head.Y - 20}, ImColor(0, 0, 0, 255), 背敌距离);
        }
        else if (W > 0 && X > PX * 2)
        {
          ImGui::GetForegroundDrawList()->AddCircleFilled({PX * 2, t_骨骼数据.Head.Y}, 60, ImColor(255, 255, 255, 255));
          ImGui::GetForegroundDrawList()->AddText(NULL, 25, {PX * 2 - 45, t_骨骼数据.Head.Y - 20}, ImColor(0, 0, 0, 255), 背敌距离);
        }
        else if (W > 0 && Y + W < 0)
        {
          ImGui::GetForegroundDrawList()->AddCircleFilled({t_骨骼数据.Head.X, 0}, 60, ImColor(255, 255, 255, 255));
          ImGui::GetForegroundDrawList()->AddText(NULL, 25, {t_骨骼数据.Head.X - 25, 10}, ImColor(0, 0, 0, 255), 背敌距离);
        }
        else if (W < 0)
        {
          ImGui::GetForegroundDrawList()->AddCircleFilled({PX * 2 - t_骨骼数据.Head.X, PY * 2}, 60, ImColor(255, 255, 255, 255));
          ImGui::GetForegroundDrawList()->AddText(NULL, 25, {PX * 2 - t_骨骼数据.Head.X - 25, PY * 2 - 30}, ImColor(0, 0, 0, 255), 背敌距离);
        }
      }

      if (t_屏幕坐标.W >= 0)
      {
        if (按钮.方框)
          绘图.绘制方框(对象信息.敌人信息.isboot);
        if (按钮.射线)
          绘图.绘制射线(t_骨骼数据);
        if (按钮.名字)
        {
          绘图.绘制名字(对象信息.敌人信息.名字, 对象信息.敌人信息.isboot, 0, false, ClassName, 对象信息.敌人信息.队伍, Bonecount);
        }
        if (按钮.距离)
          绘图.绘制距离(对象信息.敌人信息.距离, 对象信息.敌人信息.队伍);
        if (按钮.血量)
          绘图.绘制血量(对象信息.敌人信息.最大血量, 对象信息.敌人信息.当前血量, 对象信息.敌人信息.isboot);
        if (按钮.骨骼)
        {
          绘图.绘制骨骼(t_骨骼数据, t_屏幕坐标, LineOfSightTo1, 对象信息.敌人信息.距离);
        }
        if (按钮.手持)
          绘图.绘制手持(对象信息.敌人信息.手持, 对象信息.敌人信息.状态, 对象信息.敌人信息.子弹数量, 对象信息.敌人信息.子弹最大数量);
      }
    }
  }
  if (自瞄.初始化 && 自瞄.准星射线 && 自瞄.瞄准目标 != -1 && 自瞄函数[自瞄.瞄准目标].准心距离 <= 自瞄.自瞄范围)
  {
    ImGui::GetForegroundDrawList()->AddLine(ImVec2(PX, PY), ImVec2(自瞄函数[自瞄.瞄准目标].对象骨骼.X, 自瞄函数[自瞄.瞄准目标].对象骨骼.Y), ImColor(255, 255, 255, 255), 2.1);
  }
  if (按钮.人数)
    绘图.绘制人数(绘制人机, 绘制真人);
  自瞄.瞄准总数量 = 自瞄.瞄准对象数量;
}

void 绘制::运行绘制()
{
  更新地址数据();
  更新对象数据();
  if (!按钮.自瞄选项 && 自瞄.初始化)
  {
    自瞄主线程();
  }
}
