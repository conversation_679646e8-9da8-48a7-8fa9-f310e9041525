//微验网络验证//
//如果是AIDE编译jni，请将原main.cpp删除，将此注入好的文件改成main.cpp
#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <fcntl.h>
#include <dirent.h>
#include <pthread.h>
#include <fstream>
#include <string.h>
#include <time.h>
#include <malloc.h>
#include <iostream>
#include <fstream>
#include "weiyan.h"
#include "cJSON.h"
#include "Encrypt.h"
#include<iostream>
#include<ctime>
using namespace std;

// 定义全局变量用于存储到期时间
char _buffer[128];

// 错误处理函数
void cleanup_memory(char* _Kami, char* _Imei, char* _Value, char* _Sign, char* _Data, char* _deData, char* _deUrl) {
    if(_Kami) delete[] _Kami;
    if(_Imei) delete[] _Imei;
    if(_Value) delete[] _Value;
    if(_Sign) delete[] _Sign;
    if(_Data) delete[] _Data;
    if(_deData) delete[] _deData;
    if(_deUrl) delete[] _deUrl;
}

// 安全的字符串复制函数
size_t safe_strncpy(char* dest, const char* src, size_t size) {
    if (!dest || !src || size == 0) return 0;
    size_t i;
    for (i = 0; i < size - 1 && src[i] != '\0'; i++) {
        dest[i] = src[i];
    }
    dest[i] = '\0';
    return i;
}

// 安全的字符串长度检查
size_t safe_strlen(const char* str, size_t maxlen) {
    if (!str) return 0;
    size_t len = 0;
    while (len < maxlen && str[len] != '\0') len++;
    return len;
}

int yanzheng()
{
    const static char *_wyHost = "wy.llua.cn";
	const static char *_wyAppid = "70787";	
	const static char *_wyAppkey = "de00cad2abfea9b99ab8501e5722ebbc";
	const static char *_wyRc4key = "j8d4937daeda9e05a7d0b5352d898";
	// 以上信息勿动
	const static char *_kmPath = "LoginK";
    // 卡密路径
    const static char *_imeiPath = "/storage/emulated/0/Android/LoginM";
	// 机器码路径
    // 增加缓冲区大小以确保安全
    const size_t KAMI_SIZE = 2048;
    const size_t IMEI_SIZE = 2048;
    const size_t VALUE_SIZE = 4096;
    const size_t SIGN_SIZE = 8192;
    const size_t DATA_SIZE = 16384;
    const size_t URL_SIZE = 4096;
    // 使用动态内存分配
    char* _Kami = new (std::nothrow) char[KAMI_SIZE];
    char* _Imei = new (std::nothrow) char[IMEI_SIZE];
    char* _Value = new (std::nothrow) char[VALUE_SIZE];
    char* _Sign = new (std::nothrow) char[SIGN_SIZE];
    char* _Data = new (std::nothrow) char[DATA_SIZE];
    char* _deData = new (std::nothrow) char[DATA_SIZE];
    char* _deUrl = new (std::nothrow) char[URL_SIZE];
    if (!_Kami || !_Imei || !_Value || !_Sign || !_Data || !_deData || !_deUrl) {
        printf("\033[31;1m内存分配失败\n");
        cleanup_memory(_Kami, _Imei, _Value, _Sign, _Data, _deData, _deUrl);
        return 0;
    }
    while(true) {
        // 初始化缓冲区
        memset(_Kami, 0, KAMI_SIZE);
        memset(_Imei, 0, IMEI_SIZE);
        memset(_Value, 0, VALUE_SIZE);
        memset(_Sign, 0, SIGN_SIZE);
        memset(_Data, 0, DATA_SIZE);
        memset(_deData, 0, DATA_SIZE);
        memset(_deUrl, 0, URL_SIZE);
        if (fopen(_kmPath, "r") == NULL)
        {
            printf("\033[31;1m");
            printf("请输入卡密:");
            char* _inputKm = new (std::nothrow) char[KAMI_SIZE];
            if (!_inputKm) {
                printf("内存分配失败\n");
                cleanup_memory(_Kami, _Imei, _Value, _Sign, _Data, _deData, _deUrl);
                return 0;
            }
            memset(_inputKm, 0, KAMI_SIZE);
            
            char temp[KAMI_SIZE];
            if(scanf("%2047s", temp) != 1) {
                delete[] _inputKm;
                printf("读取卡密失败\n");
                continue;
            }
            safe_strncpy(_inputKm, temp, KAMI_SIZE);
            
            FILE *fp = fopen(_kmPath, "w");
            if (fp != NULL) {
                fprintf(fp, "%s", _inputKm);
                fclose(fp);
            }
            delete[] _inputKm;
            std::cout << "写入成功！正在验证卡密" << std::endl;
        }
        
        FILE* kmFile = fopen(_kmPath, "r");
        if (kmFile == NULL) {
            printf("\033[31;1m");
            printf("读取卡密失败\n");
            continue;
        }
        
        char temp[KAMI_SIZE];
        if(fgets(temp, KAMI_SIZE-1, kmFile) == NULL) {
            fclose(kmFile);
            printf("读取卡密失败\n");
            continue;
        }
        // 移除可能的换行符
        char* newline = strchr(temp, '\n');
        if(newline) *newline = '\0';
        safe_strncpy(_Kami, temp, KAMI_SIZE);
        fclose(kmFile);
        
        if (fopen(_imeiPath, "r") == NULL)
        {
            printf("\033[31;1m");
            printf("设备码获取失败\n");
            srand(time(NULL));
            char* _Str = new (std::nothrow) char[21];
            if (!_Str) {
                printf("内存分配失败\n");
                continue;
            }
            for (int i = 0; i < 20; i++) {
                int _randomNum = rand() % 26;
                _Str[i] = 'a' + _randomNum;
            }
            _Str[20] = '\0';
        
            FILE *fp = fopen(_imeiPath, "w");
            if (fp == NULL) {
                delete[] _Str;
                printf("文件创建失败\n");
                continue;
            }
            fprintf(fp, "%s", _Str);
            fclose(fp);
            delete[] _Str;
            std::cout << "设备码已重新获取！正在验证卡密" << std::endl;
        }
        
        FILE* imeiFile = fopen(_imeiPath, "r");
        if (imeiFile == NULL) {
            printf("\033[31;1m");
            printf("读取设备码失败\n");
            continue;
        }
        
        memset(temp, 0, IMEI_SIZE);
        if(fgets(temp, IMEI_SIZE-1, imeiFile) == NULL) {
            fclose(imeiFile);
            printf("读取设备码失败\n");
            continue;
        }
        newline = strchr(temp, '\n');
        if(newline) *newline = '\0';
        safe_strncpy(_Imei, temp, IMEI_SIZE);
        fclose(imeiFile);
        
        if (safe_strlen(_Kami, KAMI_SIZE) == 0 || safe_strlen(_Imei, IMEI_SIZE) == 0)
        {
            printf("\033[31;1m");
            printf("无设备码或者卡密\n");
            remove(_kmPath);
            continue;
        }
        
        time_t _Timet = time(NULL);
        int _Time = time(&_Timet);
        srand(time(NULL));
        
        if(snprintf(_Value, VALUE_SIZE, "%d%d", _Time, rand()) >= VALUE_SIZE) {
            printf("Value 缓冲区溢出\n");
            continue;
        }
        
        if(snprintf(_Sign, SIGN_SIZE, "kami=%s&markcode=%s&t=%d&%s", 
                   _Kami, _Imei, _Time, _wyAppkey) >= SIGN_SIZE) {
            printf("Sign 缓冲区溢出\n");
            continue;
        }
        
        unsigned char* _Decrypt = new (std::nothrow) unsigned char[16];
        char* _SignMd5 = new (std::nothrow) char[33];
        if (!_Decrypt || !_SignMd5) {
            if (_Decrypt) delete[] _Decrypt;
            if (_SignMd5) delete[] _SignMd5;
            printf("内存分配失败\n");
            continue;
        }
        memset(_SignMd5, 0, 33);
        
        MD5_CTX md5c;
        MD5Init(&md5c);
        MD5Update(&md5c, (unsigned char*)_Sign, strlen(_Sign));
        MD5Final(&md5c, _Decrypt);
        
        for (int i = 0; i < 16; i++) {
            snprintf(&_SignMd5[i * 2], 3, "%02x", _Decrypt[i]);
        }
        _SignMd5[32] = '\0';
        
        if(snprintf(_Data, DATA_SIZE, "kami=%s&markcode=%s&t=%d&sign=%s&value=%s", 
                   _Kami, _Imei, _Time, _SignMd5, _Value) >= DATA_SIZE) {
            delete[] _Decrypt;
            delete[] _SignMd5;
            printf("Data 缓冲区溢出\n");
            continue;
        }
        
        char* _enData = Encrypt(_Data, _wyRc4key);
        if(_enData == NULL) {
            delete[] _Decrypt;
            delete[] _SignMd5;
            printf("加密失败\n");
            continue;
        }
        
        if(snprintf(_deData, DATA_SIZE, "&data=%s", _enData) >= DATA_SIZE) {
            delete[] _Decrypt;
            delete[] _SignMd5;
            printf("deData 缓冲区溢出\n");
            continue;
        }
        
        if(snprintf(_deUrl, URL_SIZE, "api/?id=kmlogin&app=%s", _wyAppid) >= URL_SIZE) {
            delete[] _Decrypt;
            delete[] _SignMd5;
            printf("URL 缓冲区溢出\n");
            continue;
        }
        
        char* _loginData = httppost(_wyHost, _deUrl, _deData);
        if(_loginData == NULL) {
            delete[] _Decrypt;
            delete[] _SignMd5;
            printf("HTTP请求失败\n");
            continue;
        }
        
        char* _deloginData = Decrypt(_loginData, _wyRc4key);
        if(_deloginData == NULL) {
            delete[] _Decrypt;
            delete[] _SignMd5;
            printf("解密失败\n");
            continue;
        }
        
        delete[] _Decrypt;
        delete[] _SignMd5;
        
        cJSON* _loginJson = cJSON_Parse(_deloginData);
        if (_loginJson == NULL) {
            printf("JSON解析失败\n");
            continue;
        }
        
        int _loginCode = cJSON_GetObjectItem(_loginJson, "x5d6f2d07c728402fd052d77370159835")->valueint;
        int _loginTime = cJSON_GetObjectItem(_loginJson, "b7261ad3307613cd249cc682fc9160d1d")->valueint;
        char *_loginMsg = cJSON_GetObjectItem(_loginJson, "y713827baf7d37d01d6f7ca10d0a6d247")->valuestring;
        char *_loginCheck = cJSON_GetObjectItem(_loginJson, "yc71db95b46cb1502d9027ab6b8158fa5")->valuestring;
        
        if (_loginCode == 54378)
        {
            cJSON *_loginMsgs = cJSON_GetObjectItem(_loginJson, "y713827baf7d37d01d6f7ca10d0a6d247");
            char *_checkCode = cJSON_GetObjectItem(_loginMsgs, "wab1ffe44d6d0b1f01becc4ebae35bd81")->valuestring;
            long _loginVip = cJSON_GetObjectItem(_loginMsgs, "g89258d0dfa702fb1b18a207269460a31")->valuedouble;
            long _loginId = cJSON_GetObjectItem(_loginMsgs, "zf23bdb7572423787e4d527e53113838a")->valuedouble;
            char _deCheck[1024];
            sprintf(_deCheck, "%d%s%s",_loginTime,_wyAppkey,_Value);
            unsigned char *_deCheckData = (unsigned char *)_deCheck;
            MD5_CTX md5c;
            MD5Init(&md5c);
            unsigned char _Decrypt[16];
            MD5Update(&md5c, _deCheckData, strlen((char *)_deCheckData));
            MD5Final(&md5c, _Decrypt);
            char _checkMd5[33] = { 0 };
            for (int i = 0; i < 16; i++)
            {
                sprintf(&_checkMd5[i * 2], "%02x", _Decrypt[i]);
            }
            if ((string)_checkCode != "66b94fcaf327b9398f6cd501ed07c42b"){
                printf("\033[31;1m");
                printf("校验码错误\n");
                remove(_kmPath);
                continue;  // 重新开始验证
            }
            if ((string)_checkMd5 == _loginCheck)
            {
                printf("\033[32;1m");	// 绿色
                printf("登录成功\n");
                if (_loginVip)
                {
                    char _vipTime[11];
                    sprintf(_vipTime, "%ld", _loginVip);
                    time_t _timeStamp = std::atoll(_vipTime);
                    time_t remainingSeconds = _timeStamp - _Time;
                    int days = remainingSeconds / (24*3600);
                    int hours = (remainingSeconds % (24*3600)) / 3600;
                    int minutes = (remainingSeconds % 3600) / 60;
                    int seconds = remainingSeconds % 60;
                    sprintf(_buffer, "%d天%d小时%d分钟%d秒", days, hours, minutes, seconds);
                    printf("\033[32;1m");  // 绿色
                    printf("%s\n", _buffer);
                    //到期自动退出
                    signal(SIGALRM, _exit); 
                    alarm(_loginVip-_Time); 
                }
                // 成功后释放内存
                cleanup_memory(_Kami, _Imei, _Value, _Sign, _Data, _deData, _deUrl);
                return 1;  // 只有验证成功才返回
            }
            else
            {
                printf("\033[31;1m");
                printf("校验失败\n");
                remove(_kmPath);
                continue;  // 重新开始验证
            }
        }
        else
        {
            printf("\033[35;1m");	// 粉红色
            cout << _loginMsg << endl;
            remove(_kmPath);
            continue;  // 重新开始验证
        }
    }
    
    cleanup_memory(_Kami, _Imei, _Value, _Sign, _Data, _deData, _deUrl);
    return 0; 
}