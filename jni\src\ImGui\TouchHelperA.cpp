/*
 * TouchHelperA.cpp - 触摸事件处理助手类
 *
 * 功能说明：
 * 1. 处理Android设备的触摸输入事件
 * 2. 支持多点触控和触摸事件拦截
 * 3. 提供触摸坐标转换和屏幕适配
 * 4. 集成ImGui触摸事件处理
 * 5. 支持虚拟触摸设备创建和管理
 */

#include <stdio.h>
#include <stdlib.h>
#include <dirent.h>
#include <fcntl.h>
#include <unistd.h>
#include <pthread.h>
#include <cmath>
#include <linux/input.h>
#include <linux/uinput.h>

#include "src/辅助类.h"

#include "imgui.h"
#include "imgui_impl_android.h"
#include "imgui_impl_opengl3.h"

// 宏定义
#define maxE 5      // 最大事件设备数量
#define maxF 10     // 最大手指数量
#define UNGRAB 0    // 释放设备控制
#define GRAB 1      // 获取设备控制

// 全局触摸状态变量
int Touch_ID;                    // 当前触摸ID
int Touch_I;                     // 触摸事件计数器
int Touch_I_bak;                 // 触摸事件计数器备份
int Touch_Global_SLOT;           // 全局触摸槽位
int Touch_Temporary_SLOT;        // 临时触摸槽位
int Touch_Temporary_SLOT_Bak;    // 临时触摸槽位备份
Vector2 Touch_Clicks;            // 触摸点击坐标

// 外部变量引用
extern bool 悬浮窗;              // 悬浮窗显示状态
extern 绘制 绘制;                // 绘制对象

bool other_touch;                // 其他触摸标志

// 静态变量
static uint32_t orientation = 0;        // 屏幕方向
static float screenHeight = 0, screenWidth = 0;  // 屏幕尺寸

// 触摸对象结构体 - 存储单个触摸点的信息
struct touchObj {
  bool isDown = false;      // 是否按下状态
  bool isTmpDown = false;   // 临时按下状态
  int x = 0;                // X坐标
  int y = 0;                // Y坐标
  int id = 0;               // 触摸点ID
  int size1 = 0;            // 触摸主要尺寸
  int size2 = 0;            // 触摸宽度尺寸
  int size3 = 0;            // 触摸次要尺寸
};

// 线程参数结构体 - 传递给触摸处理线程的参数
struct targ {
  int fdNum;                // 文件描述符编号
  float S2TX;               // 屏幕到触摸X轴缩放比例
  float S2TY;               // 屏幕到触摸Y轴缩放比例
};

// 输入事件结构体 - 管理输入事件数据
static struct {
  // 按下事件数组：BTN_TOUCH和BTN_TOOL_FINGER事件
  input_event downEvent[2]{{{}, EV_KEY, BTN_TOUCH,       1},
    {{}, EV_KEY, BTN_TOOL_FINGER, 1}};
  input_event event[512]{0};  // 事件缓冲区，最多512个事件
} input;

// 静态全局变量
static targ targF[maxE];                    // 线程参数数组，每个触摸设备一个
static touchObj Finger[maxE][maxF];         // 触摸对象二维数组：[设备][手指]
static int fdNum = 0, origfd[maxE], nowfd;  // 文件描述符：设备数量、原始fd数组、当前fd
static float scale_x, scale_y;              // X和Y轴缩放比例
static bool Touch_initialized = false;      // 触摸系统初始化状态
static bool Touch_readOnly = false;         // 只读模式标志

// 函数声明
static bool checkDeviceIsTouch(int fd);     // 检查设备是否为触摸设备

/**
 * 生成随机字符串
 * @param string 输出字符串缓冲区
 * @param length 字符串长度
 *
 * 功能：生成包含大写字母、小写字母和数字的随机字符串
 * 用途：为虚拟触摸设备生成随机名称，避免被检测
 */
static void genRandomString(char *string, int length) {
  int flag, i;
  srand((unsigned) time(NULL) + length);  // 使用当前时间+长度作为随机种子
  for (i = 0; i < length - 1; i++) {
    flag = rand() % 3;  // 随机选择字符类型
    switch (flag) {
      case 0:  // 大写字母 A-Z
      string[i] = 'A' + rand() % 26;
      break;
      case 1:  // 小写字母 a-z
      string[i] = 'a' + rand() % 26;
      break;
      case 2:  // 数字 0-9
      string[i] = '0' + rand() % 10;
      break;
      default:
      string[i] = 'x';  // 默认字符
      break;
    }
  }
  string[length - 1] = '\0';  // 字符串结束符
}

/**
 * 触摸坐标转屏幕坐标
 * @param coord 输入的触摸坐标
 * @return 转换后的屏幕坐标
 *
 * 功能：根据屏幕方向和缩放比例，将触摸坐标转换为屏幕坐标
 * 支持4种屏幕方向：0(默认)、1(90度)、2(180度)、3(270度)
 */
Vector2 Touch2Screen(const Vector2 &coord) {
  float x = coord.x, y = coord.y;
  float xt = x / scale_x;  // 应用X轴缩放
  float yt = y / scale_y;  // 应用Y轴缩放

  if (other_touch) {  // 其他触摸模式的坐标转换
    switch (orientation) {
      case 1:  // 90度旋转
      x = xt;
      y = yt;
      break;
      case 2:  // 180度旋转
      y = yt;
      x = screenHeight - xt;
      break;
      case 3:  // 270度旋转
      x = screenHeight - xt;
      y = screenWidth - yt;
      break;
      default:  // 默认方向
      y = xt;
      x = screenHeight - yt;
      break;
    }
  } else {  // 标准触摸模式的坐标转换
    switch (orientation) {
      case 1:  // 90度旋转
      x = yt;
      y = screenHeight - xt;
      break;
      case 2:  // 180度旋转
      x = screenHeight - xt;
      y = screenWidth - yt;
      break;
      case 3:  // 270度旋转
      y = xt;
      x = screenWidth - yt;
      break;
      default:  // 默认方向
      x = xt;
      y = yt;
      break;
    }
  }
  return {x, y};  // 返回转换后的坐标
}

/**
 * 上传触摸事件到系统
 *
 * 功能：将当前所有活跃的触摸点信息打包成input_event事件并发送到系统
 * 包含多点触控支持、触摸尺寸信息、同步事件等
 * 使用线程安全机制防止并发访问冲突
 */
static void Upload() {
  static bool bTouch = false;      // 触摸上传锁，防止并发
  static bool isFirstDown = true;  // 首次按下标志
  while (bTouch);  // 等待上一次上传完成
  bTouch = true;   // 设置上传锁
  int tmpCnt = 0, tmpCnt2 = 0, i, j;  // 计数器和循环变量
  // 遍历所有设备和手指，构建触摸事件
  for (i = 0; i < fdNum; i++) {
    for (j = 0; j < maxF; j++) {
      if (Finger[i][j].isDown) {  // 如果手指处于按下状态
        if (tmpCnt2++ > 10) {     // 限制最大触摸点数量为10个
          goto finish;
        }

        // 设置绝对坐标X轴事件
        input.event[tmpCnt].type = EV_ABS;
        input.event[tmpCnt].code = ABS_X;
        input.event[tmpCnt].value = Finger[i][j].x;
        tmpCnt++;

        // 设置绝对坐标Y轴事件
        input.event[tmpCnt].type = EV_ABS;
        input.event[tmpCnt].code = ABS_Y;
        input.event[tmpCnt].value = Finger[i][j].y;
        tmpCnt++;

        // 设置多点触控X轴位置事件
        input.event[tmpCnt].type = EV_ABS;
        input.event[tmpCnt].code = ABS_MT_POSITION_X;
        input.event[tmpCnt].value = Finger[i][j].x;
        tmpCnt++;

        // 设置多点触控Y轴位置事件
        input.event[tmpCnt].type = EV_ABS;
        input.event[tmpCnt].code = ABS_MT_POSITION_Y;
        input.event[tmpCnt].value = Finger[i][j].y;
        tmpCnt++;

        // 设置多点触控跟踪ID事件
        input.event[tmpCnt].type = EV_ABS;
        input.event[tmpCnt].code = ABS_MT_TRACKING_ID;
        input.event[tmpCnt].value = Finger[i][j].id;
        tmpCnt++;

        // 设置触摸主要尺寸（如果有值）
        if (Finger[i][j].size1)
        {
          input.event[tmpCnt].type = EV_ABS;
          input.event[tmpCnt].code = ABS_MT_TOUCH_MAJOR;
          input.event[tmpCnt].value = Finger[i][j].size1;
          tmpCnt++;
        }

        // 设置触摸宽度尺寸（如果有值）
        if (Finger[i][j].size2)
        {
          input.event[tmpCnt].type = EV_ABS;
          input.event[tmpCnt].code = ABS_MT_WIDTH_MAJOR;
          input.event[tmpCnt].value = Finger[i][j].size2;
          tmpCnt++;
        }

        // 设置触摸次要尺寸（如果有值）
        if (Finger[i][j].size3)
        {
          input.event[tmpCnt].type = EV_ABS;
          input.event[tmpCnt].code = ABS_MT_TOUCH_MINOR;
          input.event[tmpCnt].value = Finger[i][j].size3;
          tmpCnt++;
        }

        // 添加多点触控报告同步事件
        input.event[tmpCnt].type = EV_SYN;
        input.event[tmpCnt].code = SYN_MT_REPORT;
        input.event[tmpCnt].value = 0;
        tmpCnt++;
      }
    }
  }

  finish:  // 完成事件构建的标签
  bool is = false;  // 是否有触摸事件标志

  if (tmpCnt == 0) {  // 如果没有触摸事件
    // 添加空的多点触控报告
    input.event[tmpCnt].type = EV_SYN;
    input.event[tmpCnt].code = SYN_MT_REPORT;
    input.event[tmpCnt].value = 0;
    tmpCnt++;

    // 如果之前有触摸，现在需要发送抬起事件
    if (!isFirstDown) {
      isFirstDown = true;
      // 发送触摸抬起事件
      input.event[tmpCnt].type = EV_KEY;
      input.event[tmpCnt].code = BTN_TOUCH;
      input.event[tmpCnt].value = 0;  // 0表示抬起
      tmpCnt++;
      // 发送手指工具抬起事件
      input.event[tmpCnt].type = EV_KEY;
      input.event[tmpCnt].code = BTN_TOOL_FINGER;
      input.event[tmpCnt].value = 0;  // 0表示抬起
      tmpCnt++;
    }
  } else {
    is = true;  // 标记有触摸事件
  }

  // 添加同步报告事件，表示一帧触摸数据结束
  input.event[tmpCnt].type = EV_SYN;
  input.event[tmpCnt].code = SYN_REPORT;
  input.event[tmpCnt].value = 0;
  tmpCnt++;

  // 写入触摸事件到设备
  if (is && isFirstDown) {  // 如果是首次按下
    isFirstDown = false;
    // 包含按下事件一起发送
    write(nowfd, &input, sizeof(struct input_event) * (tmpCnt + 2));
  } else {
    // 只发送当前事件
    write(nowfd, input.event, sizeof(struct input_event) * tmpCnt);
  }

  bTouch = false;  // 释放触摸上传锁
}

static void *TypeA(void *arg) {
  targ tmp = *(targ *) arg;
  int i = tmp.fdNum;
  float S2TX = tmp.S2TX;
  float S2TY = tmp.S2TY;
  int latest = 0;
  input_event inputEvent[64]{0};
  
  timer touchFPS;
  touchFPS.SetFps(800);
  touchFPS.AotuFPS_init();
  touchFPS.setAffinity();
  
  while (Touch_initialized) {
    ImGuiIO &io = ImGui::GetIO();
    auto readSize = (int32_t) read(origfd[i], inputEvent, sizeof(inputEvent));
    if (readSize <= 0 || (readSize % sizeof(input_event)) != 0) {
      continue;
    }
    size_t count = size_t(readSize) / sizeof(input_event);
    for (size_t j = 0; j < count; j++) {
      input_event &ie = inputEvent[j];
      if (ie.type == EV_ABS) {
        if (ie.code == ABS_MT_SLOT) {
          latest = ie.value;
          Touch_Temporary_SLOT = ie.value;
          Touch_I++;
          continue;
        }
        if (ie.code == ABS_MT_TRACKING_ID) {
          if (ie.value == -1) {
            Finger[i][latest].isDown = false;
            io.MouseDown[0] = false;
            Touch_Temporary_SLOT_Bak = Touch_Temporary_SLOT;
            Touch_ID = -1;
          } else {
            Finger[i][latest].id = (i * 2 + 1) * maxF + latest;
            Touch_ID = ie.value;
            Touch_I_bak = Touch_I;
            Touch_Temporary_SLOT_Bak = Touch_Temporary_SLOT;
            Finger[i][latest].isDown = true;
            io.MouseDown[0] = true;
          }
          Touch_I++;
          continue;
        }
        if (ie.code == ABS_MT_POSITION_X) {
          Finger[i][latest].id = (i * 2 + 1) * maxF + latest;
          Finger[i][latest].x = (int) (ie.value * S2TX);
          if(Touch_I == Touch_I_bak+1) {
            Touch_Clicks.x = (float) ie.value * S2TX;
          }
          Touch_I++;
          Finger[i][latest].isTmpDown = true;
          continue;
        }
        if (ie.code == ABS_MT_POSITION_Y) {
          Finger[i][latest].id = (i * 2 + 1) * maxF + latest;
          Finger[i][latest].y = (int) (ie.value * S2TY);
          
          if(Touch_I == Touch_I_bak+2)
          {
            Touch_Clicks.y = (float) ie.value * S2TY;
            Touch_Clicks = Touch2Screen(Touch_Clicks);
          }
          Touch_I++;
          Finger[i][latest].isTmpDown = true;
          continue;
        }
        if (ie.code == ABS_MT_TOUCH_MAJOR)
        {
          Finger[i][latest].id = (i * 2 + 1) * maxF + latest;
          Finger[i][latest].size1 = ie.value;
          Finger[i][latest].isTmpDown = true;
          continue;
        }
        if (ie.code == ABS_MT_WIDTH_MAJOR)
        {
          Finger[i][latest].id = (i * 2 + 1) * maxF + latest;
          Finger[i][latest].size2 = ie.value;
          Finger[i][latest].isTmpDown = true;
          continue;
        }
        if (ie.code == ABS_MT_TOUCH_MINOR)
        {
          Finger[i][latest].id = (i * 2 + 1) * maxF + latest;
          Finger[i][latest].size3 = ie.value;
          Finger[i][latest].isTmpDown = true;
          continue;
        }
        
      }
      
      
      float x = Finger[i][latest].x, y = Finger[i][latest].y;
      float xt = x / scale_x;
      float yt = y / scale_y;
      if (other_touch) {
        switch (orientation) {
          case 1:
          x = xt;
          y = yt;
          break;
          case 2:
          y = yt;
          x = screenHeight - xt;
          break;
          case 3:
          x = screenHeight - xt;
          y = screenWidth - yt;
          break;
          default:
          y = xt;
          x = screenHeight - yt;
          break;
        }
      } else {
        switch (orientation) {
          case 1:
          x = yt;
          y = screenHeight - xt;
          break;
          case 2:
          x = screenHeight - xt;
          y = screenWidth - yt;
          break;
          case 3:
          y = xt;
          x = screenWidth - yt;
          break;
          default:
          x = xt;
          y = yt;
          break;
        }
      }
      io.MousePos = {x, y};
      
      // 只在悬浮窗显示时拦截触摸
      if (悬浮窗 && io.MousePos.x <= 绘制.Pos.x + 绘制.winWidth && io.MousePos.y <= 绘制.Pos.y + 绘制.winHeith && io.MousePos.x >= 绘制.Pos.x && io.MousePos.y >= 绘制.Pos.y)
      {
        Finger[i][latest].isDown = false;
        Finger[i][latest].isTmpDown = false;
      }
      
      if (ie.type == EV_SYN)
      {
        if (ie.code == SYN_REPORT)
        {
          if (Finger[i][latest].isTmpDown)
          Upload();
          continue;
        }
        continue;
      }
      
      
    }
    touchFPS.SetFps((int)绘制.自瞄.触摸采样率);
    touchFPS.AotuFPS();
  }
  return nullptr;
}


bool Touch_Init(int w, int h, uint32_t orientation_, bool readOnly) {
  char temp[128];
  DIR *dir = opendir("/dev/input/");
  dirent *ptr = NULL;
  int eventCount = 0;
  while ((ptr = readdir(dir)) != NULL) {
    if (strstr(ptr->d_name, "event"))
    eventCount++;
  }
  struct input_absinfo abs, absX[maxE], absY[maxE];
  int fd, i, tmp1, tmp2;
  int screenX, screenY, minCnt = eventCount + 1;
  fdNum = 0;
  for (i = 0; i <= eventCount; i++) {
    sprintf(temp, "/dev/input/event%d", i);
    fd = open(temp, O_RDWR);
    if (fd < 0) {
      continue;
    }
    if (checkDeviceIsTouch(fd)) {
      tmp1 = ioctl(fd, EVIOCGABS(ABS_MT_POSITION_X), &absX[fdNum]);
      tmp2 = ioctl(fd, EVIOCGABS(ABS_MT_POSITION_Y), &absY[fdNum]);
      if (tmp1 == 0 && tmp2 == 0) {
        origfd[fdNum] = fd;
        if (!readOnly) {
          ioctl(fd, EVIOCGRAB, GRAB);
        }
        if (i < minCnt) {
          screenX = absX[fdNum].maximum;
          screenY = absY[fdNum].maximum;
          minCnt = i;
        }
        fdNum++;
        if (fdNum >= maxE)
        break;
      }
    } else {
      close(fd);
    }
  }
  
  if (minCnt > eventCount) {
    puts("获取屏幕驱动失败");
    return false;
  }
  
  if (!readOnly) {
    struct uinput_user_dev ui_dev;
    nowfd = open("/dev/uinput", O_WRONLY | O_NONBLOCK);
    if (nowfd <= 0) {
      return false;
    }
    
    int string_len = rand() % 10 + 5;
    char string[string_len];
    memset(&ui_dev, 0, sizeof(ui_dev));
    
    genRandomString(string, string_len);
    strncpy(ui_dev.name, string, UINPUT_MAX_NAME_SIZE);
    
    ui_dev.id.bustype = 0;
    ui_dev.id.vendor = rand() % 10 + 5;
    ui_dev.id.product = rand() % 10 + 5;
    ui_dev.id.version = rand() % 10 + 5;
    
    ioctl(nowfd, UI_SET_PROPBIT, INPUT_PROP_DIRECT);
    
    ioctl(nowfd, UI_SET_EVBIT, EV_ABS);
    ioctl(nowfd, UI_SET_ABSBIT, ABS_X);
    ioctl(nowfd, UI_SET_ABSBIT, ABS_Y);
    ioctl(nowfd, UI_SET_ABSBIT, ABS_MT_POSITION_X);
    ioctl(nowfd, UI_SET_ABSBIT, ABS_MT_POSITION_Y);
    ioctl(nowfd, UI_SET_ABSBIT, ABS_MT_TRACKING_ID);
    ioctl(nowfd, UI_SET_ABSBIT, ABS_MT_TOUCH_MAJOR);
    ioctl(nowfd, UI_SET_ABSBIT, ABS_MT_WIDTH_MAJOR);
    ioctl(nowfd, UI_SET_ABSBIT, ABS_MT_TOUCH_MINOR);
    ioctl(nowfd, UI_SET_EVBIT, EV_SYN);
    ioctl(nowfd, UI_SET_EVBIT, EV_KEY);
    ioctl(nowfd, UI_SET_KEYBIT, BTN_TOOL_FINGER);
    ioctl(nowfd, UI_SET_KEYBIT, BTN_TOUCH);
    
    // 设置设备属性，禁止其他程序访问
    ioctl(nowfd, UI_SET_PROPBIT, INPUT_PROP_DIRECT);
    
    
    
    genRandomString(string, string_len);
    ioctl(nowfd, UI_SET_PHYS, string);
    
    sprintf(temp, "/dev/input/event%d", minCnt);
    fd = open(temp, O_RDWR);
    if (fd) {
      struct input_id id;
      if (!ioctl(fd, EVIOCGID, &id)) {
        ui_dev.id.bustype = id.bustype;
        ui_dev.id.vendor = id.vendor;
        ui_dev.id.product = id.product;
        ui_dev.id.version = id.version;
      }
      uint8_t *bits = NULL;
      ssize_t bits_size = 0;
      int res, j, k;
      while (1) {
        res = ioctl(fd, EVIOCGBIT(EV_KEY, bits_size), bits);
        if (res < bits_size)
        break;
        bits_size = res + 16;
        bits = (uint8_t *) realloc(bits, bits_size * 2);
      }
      for (j = 0; j < res; j++) {
        for (k = 0; k < 8; k++)
        if (bits[j] & 1 << k) {
          if (j * 8 + k == BTN_TOUCH || j * 8 + k == BTN_TOOL_FINGER)
          continue;
          ioctl(nowfd, UI_SET_KEYBIT, j * 8 + k);
        }
      }
      free(bits);
    }
    ui_dev.absmin[ABS_MT_POSITION_X] = 0;
    ui_dev.absmax[ABS_MT_POSITION_X] = screenX;
    ui_dev.absmin[ABS_MT_POSITION_Y] = 0;
    ui_dev.absmax[ABS_MT_POSITION_Y] = screenY;
    ui_dev.absmin[ABS_X] = 0;
    ui_dev.absmax[ABS_X] = screenX;
    ui_dev.absmin[ABS_Y] = 0;
    ui_dev.absmax[ABS_Y] = screenY;
    ui_dev.absmin[ABS_MT_TOUCH_MAJOR] = 0;
    ui_dev.absmax[ABS_MT_TOUCH_MAJOR] = 255;
    ui_dev.absmin[ABS_MT_WIDTH_MAJOR] = 0;
    ui_dev.absmax[ABS_MT_WIDTH_MAJOR] = 255;
    ui_dev.absmin[ABS_MT_TOUCH_MINOR] = 0;
    ui_dev.absmax[ABS_MT_TOUCH_MINOR] = 255;
    ui_dev.absmin[ABS_MT_TRACKING_ID] = 0;
    ui_dev.absmax[ABS_MT_TRACKING_ID] = 65535;
    write(nowfd, &ui_dev, sizeof(ui_dev));
    
    if (ioctl(nowfd, UI_DEV_CREATE)) {
      return false;
    }
    //ioctl(nowfd, UI_DEV_DESTROY);//创建成功后尝试注销
    
  }
  Touch_initialized = true;
  Touch_readOnly = readOnly;
  
  pthread_t t;
  for (i = 0; i < fdNum; i++) {
    targF[i].fdNum = i;
    targF[i].S2TX = (float) screenX / (float) absX[i].maximum;
    targF[i].S2TY = (float) screenY / (float) absY[i].maximum;
    pthread_create(&t, NULL, TypeA, &targF[i]);
  }
  fdNum++;
  ::screenWidth = w;
  ::screenHeight = h,
  ::orientation = orientation_;
  if (::orientation == 1 || ::orientation == 3) {
    ::scale_x = (float) screenX / h;
    ::scale_y = (float) screenY / w;
  } else {
    ::scale_x = (float) screenX / w;
    ::scale_y = (float) screenY / h;
  }
  system("chmod 000 -R /proc/bus/input/*");
  return true;
}
void UpdateScreenData(int w, int h, uint32_t orientation_) {
  ::screenWidth = w;
  ::screenHeight = h,
  ::orientation = orientation_;
}
static bool checkDeviceIsTouch(int fd) {
  uint8_t *bits = NULL;
  ssize_t bits_size = 0;
  int res, j, k;
  bool itmp = false, itmp2 = false, itmp3 = false;
  struct input_absinfo abs{};
  while (true) {
    res = ioctl(fd, EVIOCGBIT(EV_ABS, bits_size), bits);
    if (res < bits_size)
    break;
    bits_size = res + 16;
    bits = (uint8_t *) realloc(bits, bits_size * 2);
  }
  for (j = 0; j < res; j++) {
    for (k = 0; k < 8; k++)
    if (bits[j] & 1 << k && ioctl(fd, EVIOCGABS(j * 8 + k), &abs) == 0) {
      if (j * 8 + k == ABS_MT_SLOT) {
        itmp = true;
        continue;
      }
      if (j * 8 + k == ABS_MT_POSITION_X) {
        itmp2 = true;
        continue;
      }
      if (j * 8 + k == ABS_MT_POSITION_Y) {
        itmp3 = true;
        continue;
      }
    }
  }
  free(bits);
  return itmp && itmp2 && itmp3;
}

void Touch_Down(float x, float y) {
  touchObj &touch = Finger[0][9];
  touch.id = 8;
  touch.x = (int) (x * scale_x);
  touch.y = (int) (y * scale_y);
  touch.isDown = true;
  touch.size1 = 8;
  touch.size2 = 8;
  touch.size3 = 8;
  Upload();
}
void Touch_Move(float x, float y) {
  Touch_Down(x, y);
}
void Touch_Up() {
  //printf("测试 %d / %d\n",maxE,maxF);
  touchObj &touch = Finger[0][9];
  touch.isDown = false;
  Upload();
  Upload();
}