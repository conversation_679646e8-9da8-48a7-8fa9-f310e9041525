#include "imgui.h"
#include "图片调用.h"
#include "辅助类.h"
#include <dirent.h>
#include <fcntl.h>
#include <linux/input.h>
#include <unistd.h>
#include <cstring>
#include <iostream>

extern 绘制 绘制;
bool 悬浮窗 = false;
static bool 悬浮球 = true;
static bool 自瞄控件 = false;
static int style_idx = 0;
// ImVec2 Pos; //窗口位置
ImVec2 Pos2;
ImVec2 windowSize, windowSize_max;
static bool IsBall = false;
static bool 窗口状态 = false;
static bool show_ChildMenu1 = true;
static bool show_ChildMenu2 = false;
static bool show_ChildMenu3 = false;
static bool show_ChildMenu4 = false;
static bool show_ChildMenu5 = false;

static int 自瞄窗口 = 0;
static int 配置窗口 = 0;
static int 物资窗口 = 0;
static bool 广角设置 = false;

ImU32 c_透明 = IM_COL32(0, 0, 0, 0);
ImU32 c_fafafa = IM_COL32(250, 250, 250, 255);
ImU32 c_cccccc = IM_COL32(204, 204, 204, 255);
ImU32 c_c2c2c2 = IM_COL32(194, 194, 194, 255);
ImU32 c_23292e = IM_COL32(35, 41, 46, 255);
ImU32 c_4023292e = IM_COL32(35, 41, 46, 125);
ImU32 c_eeeeee = IM_COL32(0xee, 0xee, 0xee, 255);
ImU32 c_2f363c = IM_COL32(47, 54, 60, 255);
ImU32 c_402f363c = IM_COL32(47, 54, 60, 125);
ImU32 c_DAB123 = IM_COL32(218, 177, 35, 255);
ImU32 c_DCD4CA = IM_COL32(220, 212, 202, 255);
ImU32 c_2C88E2 = IM_COL32(44, 136, 226, 255);
ImU32 c_2C88E240 = IM_COL32(44, 136, 226, 128);
ImU32 c_545D6D = IM_COL32(84, 93, 109, 255);
ImU32 c_545D6D40 = IM_COL32(84, 93, 109, 128);
ImU32 c_FFD49F = IM_COL32(255, 212, 159, 255);
ImU32 c_FFD49F80 = IM_COL32(255, 212, 159, 128);
ImU32 c_16b777 = IM_COL32(0x16, 0xB7, 0x77, 255);
ImU32 c_16b77780 = IM_COL32(0x16, 0xB7, 0x77, 128);
ImU32 c_11243B = IM_COL32(0x11, 0x24, 0x3B, 255);
ImU32 c_11243B80 = IM_COL32(0x11, 0x24, 0x3B, 128);
ImU32 c_31bdec = IM_COL32(0x31, 0xbd, 0xec, 255);
ImU32 c_558FFD = IM_COL32(0x55, 0x8F, 0xFD, 255);
ImU32 c_81A4DC = IM_COL32(0x81, 0xa4, 0xdc, 255);
ImVec4 透明ImVec4 = ImVec4(0, 0, 0, 0);
ImVec4 黑色ImVec4 = ImVec4(0, 0, 0, 1.0f);
ImVec4 灰色ImVec4 = ImVec4(0.1f, 0.1f, 0.1f, 1.0f);
ImVec4 灰色ImVec440 = ImVec4(0.1f, 0.1f, 0.1f, 0.5f);
ImVec4 灰色ImVec480 = ImVec4(0.1f, 0.1f, 0.1f, 0.75f);
ImVec4 亮灰色ImVec4 = ImVec4(0.5, 0.5, 0.5, 1);
ImVec4 亮黄色ImVec4 = ImVec4(1.0f, 0.831f, 0.623f, 1.0f);
ImVec4 半透明黑色ImVec4 = ImVec4(0.184314f, 0.211765f, 0.235294f, 0.5f);
ImVec4 半透明黑色ImVec480 = ImVec4(0.184314f, 0.211765f, 0.235294f, 0.75f);
ImVec4 ImVec423292e = ImVec4(0.137255f, 0.160784f, 0.180392f, 1.0f);
ImVec4 ImVec80423292e = ImVec4(0.137255f, 0.160784f, 0.180392f, 0.75f);
ImVec4 ImVec40423292e = ImVec4(0.137255f, 0.160784f, 0.180392f, 0.5f);
ImVec4 白色ImVec4 = ImVec4(1, 1, 1, 1);
ImVec4 ImVec4fafafa = ImVec4(0xfa / 255.0f, 0xfa / 255.0f, 0xfa / 255.0f, 1.0f);
ImVec4 ImVec431bdec = ImVec4(0x31 / 255.0f, 0xbd / 255.0f, 0xec / 255.0f, 1.0f);
ImVec4 ImVec416b777 = ImVec4(0x16 / 255.0f, 0xB7 / 255.0f, 0x77 / 255.0f, 1.0f);
ImVec4 ImVec4c2c2c2 = ImVec4(0xC2 / 255.0f, 0xC2 / 255.0f, 0xC2 / 255.0f, 1.0f);
ImVec4 ImVec42C88E2 = ImVec4(0x2C / 255.0f, 0x88 / 255.0f, 0xE2 / 255.0f, 1.0f);
ImVec4 ImVec411243B = ImVec4(0x11 / 255.0f, 0x24 / 255.0f, 0x3B / 255.0f, 1.0f);
ImVec4 ImVec411243B80 = ImVec4(0x11 / 255.0f, 0x24 / 255.0f, 0x3B / 255.0f, 0.75f);
ImVec4 ImVec411243B40 = ImVec4(0x11 / 255.0f, 0x24 / 255.0f, 0x3B / 255.0f, 0.5f);
ImVec4 ImVec4f3c258 = ImVec4(0xf3 / 255.0f, 0xc2 / 255.0f, 0x58 / 255.0f, 1.0f);
ImVec4 ImVec4eeeeee = ImVec4(0xee / 255.0f, 0xee / 255.0f, 0xee / 255.0f, 1.0f);
ImVec4 ImVec4558FFD = ImVec4(0x55 / 255.0f, 0x8f / 255.0f, 0xff / 255.0f, 1.0f);
ImVec4 ImVec481A4DC = ImVec4(0x81 / 255.0f, 0xa4 / 255.0f, 0xdc / 255.0f, 1.0f);

static bool isInitialized = false;

void CustomNewLine(float lineHeight = -1.0f)
{
  if (lineHeight < 0.0f)
  {
    // 使用默认的行高
    ImGui::NewLine();
  }
  else
  {
    // 设置自定义行高
    ImGui::Dummy(ImVec2(0.0f, lineHeight));
  }
}

void RadioGroup(const char *label, int *selectedIndex, const char *options[], int numOptions, float width, float fontSize = 1.0f, float padding = 10.0f)
{
  ImGui::SetWindowFontScale(fontSize);
  ImGui::PushID(label);
  const ImGuiStyle &style = ImGui::GetStyle();
  const ImVec2 startPos = ImGui::GetCursorScreenPos();
  const float buttonSpacing = style.ItemSpacing.x;
  const float borderRadius = 10.0f;
  const ImVec2 buttonStartPos = ImVec2(startPos.x + padding, startPos.y + padding + style.FramePadding.y);
  const ImU32 selectedColor = c_558FFD;
  const ImU32 unselectedTextColor = c_4023292e;
  const ImU32 selectedTextColor = c_fafafa;
  const float buttonWidth = (width - buttonSpacing * (numOptions - 1)) / numOptions;
  ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 0.0f);
  ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, borderRadius);
  ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(0.0f, style.ItemSpacing.y));
  float totalWidth = 0.0f;
  for (int i = 0; i < numOptions; ++i)
  {
    totalWidth += buttonWidth;
  }
  const ImVec2 bgSize(totalWidth + padding * 2.0f, style.FramePadding.y * 2.0f + ImGui::GetTextLineHeight() + padding * 2.0f);
  const ImVec2 bgStartPos = ImVec2(startPos.x - padding, startPos.y - padding);
  const ImU32 shadowColor = IM_COL32(0, 0, 0, 64);
  const ImVec2 shadowOffset(3.0f, 3.0f);
  const ImVec2 shadowStartPos = ImVec2(bgStartPos.x + shadowOffset.x, bgStartPos.y + shadowOffset.y);
  const ImVec2 shadowEndPos = ImVec2(shadowStartPos.x + bgSize.x, shadowStartPos.y + bgSize.y);
  // ImGui::GetWindowDrawList()->AddRectFilled(shadowStartPos, shadowEndPos, shadowColor, style.FrameRounding);
  ImGui::GetWindowDrawList()->AddRectFilled(bgStartPos, ImVec2(bgStartPos.x + bgSize.x, bgStartPos.y + bgSize.y), c_eeeeee, style.FrameRounding);
  for (int i = 0; i < numOptions; ++i)
  {
    if (i > 0)
    {
      ImGui::SameLine();
    }
    if (*selectedIndex == i)
    {
      ImGui::PushStyleColor(ImGuiCol_Button, selectedColor);
      ImGui::PushStyleColor(ImGuiCol_ButtonHovered, selectedColor);
      ImGui::PushStyleColor(ImGuiCol_ButtonActive, selectedColor);
      ImGui::PushStyleColor(ImGuiCol_Text, selectedTextColor);
    }
    else
    {
      ImGui::PushStyleColor(ImGuiCol_Button, IM_COL32(0, 0, 0, 0));        // 透明
      ImGui::PushStyleColor(ImGuiCol_ButtonHovered, IM_COL32(0, 0, 0, 0)); // 透明
      ImGui::PushStyleColor(ImGuiCol_ButtonActive, IM_COL32(0, 0, 0, 0));  // 透明
      ImGui::PushStyleColor(ImGuiCol_Text, unselectedTextColor);
    }
    if (ImGui::Button(options[i], ImVec2(buttonWidth, 0.0f)))
    {
      *selectedIndex = i;
    }
    ImGui::PopStyleColor(4);
  }
  ImGui::PopStyleVar(2);
  ImGui::PopStyleColor();
  ImGui::PopID();
  ImGui::SetWindowFontScale(1.0f);
}

void CustomText(const char *text, ImVec2 size, int padding = 5, int alignment = 0, float fontSize = 1.5f, bool showShadow = false, ImVec4 bgColor = ImVec4(0.0f, 0.0f, 0.0f, 0.5f), ImVec4 textColor = ImVec40423292e, ImVec4 shadowColor = ImVec4(0.0f, 0.0f, 0.0f, 0.5f))
{
  ImGui::SetWindowFontScale(fontSize);
  ImGuiWindow *window = ImGui::GetCurrentWindow();
  if (window->SkipItems)
    return;
  const ImGuiStyle &style = ImGui::GetStyle();
  const ImGuiID id = window->GetID(text);
  const ImVec2 contentMin = window->DC.CursorPos;
  const ImVec2 contentMax = ImVec2(contentMin.x + size.x, contentMin.y + size.y);
  if (showShadow)
  {
    ImDrawList *drawList = ImGui::GetWindowDrawList();
    ImU32 shadowColor2 = IM_COL32(0, 0, 0, 128);
    float shadowStartY = contentMin.y + size.y - 3;
    float shadowEndY = shadowStartY + 5;
    drawList->AddRectFilledMultiColor(
        ImVec2(contentMin.x, shadowStartY),
        ImVec2(contentMin.x + size.x, shadowEndY),
        shadowColor2, shadowColor2, shadowColor2, shadowColor2);
  }
  if (bgColor.w > 0.0f)
  {
    window->DrawList->AddRectFilled(contentMin, contentMax, ImGui::GetColorU32(bgColor));
  }
  ImVec2 textSize = ImGui::CalcTextSize(text);
  ImVec2 textPos;
  switch (alignment)
  {
  case 1:
    textPos = ImVec2(contentMax.x - textSize.x - padding, contentMin.y + (size.y - textSize.y) * 0.5f);
    break;
  case 2:
    textPos = ImVec2(contentMin.x + (size.x - textSize.x) * 0.5f, contentMin.y + (size.y - textSize.y) * 0.5f);
    break;
  default:
    textPos = ImVec2(contentMin.x + padding, contentMin.y + (size.y - textSize.y) * 0.5f);
    break;
  }
  window->DrawList->AddText(textPos, ImGui::GetColorU32(textColor), text);
  float textHeightWithPadding = textSize.y + padding * 2;
  ImGui::ItemSize(ImVec2(size.x, textHeightWithPadding), style.FramePadding.y);
  if (!ImGui::ItemAdd(ImRect(contentMin, ImVec2(contentMax.x, contentMin.y + textHeightWithPadding)), id))
    return;
  ImGui::SetWindowFontScale(1.0f);
}

void 绘制背景图(TextureInfo textureInfo, ImVec2 center, float size)
{
  ImGui::SetCursorPos({0, 180});
  ImDrawList *draw_list = ImGui::GetWindowDrawList();
  draw_list->AddImage(textureInfo.textureId, {center.x - size / 2, center.y - size / 2}, {center.x + size / 2, center.y + size / 2});
}

void 布局::绘制悬浮窗()
{
  drawBegin();

  if (绘制.按钮.绘制)
  {
    绘制.运行绘制();
  }

  if (绘制.自瞄.自瞄控件)
  {
    ImGui::SetNextWindowSize({200, 200});
    if (ImGui::Begin("自瞄控件", &自瞄控件, ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoScrollbar))
    {
      auto Pos = ImGui::GetWindowPos();

      static bool isMouseDown = false;
      static bool wasMousePressed = false;
      static bool isTe = false;
      static ImVec2 mousePressPos;
      bool mouseDown = ImGui::IsMouseDown(ImGuiMouseButton_Left);
      bool windowHovered = ImGui::IsWindowHovered();

      if (mouseDown && !isMouseDown && windowHovered && ImGui::IsMouseHoveringRect(Pos, {Pos.x + 100, Pos.y + 100}))
      {
        isMouseDown = true;
        wasMousePressed = true;
        mousePressPos = ImGui::GetMousePos();
      }
      else if (!mouseDown && isMouseDown && wasMousePressed)
      {
        ImVec2 mouseReleasePos = ImGui::GetMousePos();
        if (mousePressPos.x == mouseReleasePos.x && mousePressPos.y == mouseReleasePos.y)
        {
          isTe = !isTe;
        }

        isMouseDown = false;
        wasMousePressed = false;
      }

      if (isTe)
      {
        绘制.自瞄.初始化 = true;
        ImGui::GetWindowDrawList()->AddImage(手持图片[4].textureId, {Pos.x + 20, Pos.y + 20}, {Pos.x + 120, Pos.y + 120});
      }
      else
      {
        绘制.自瞄.初始化 = false;
        ImGui::GetWindowDrawList()->AddImage(手持图片[3].textureId, {Pos.x + 20, Pos.y + 20}, {Pos.x + 120, Pos.y + 120});
      }
    }
    ImGui::End();
  }

  // 只显示悬浮窗，不需要额外的悬浮球窗口
  if (悬浮窗)
  {
    ImGuiStyle &style = ImGui::GetStyle();
    style.WindowRounding = 20.0f;

    ImVec2 minSize = ImVec2(1080, 780);
    ImVec2 maxSize = ImVec2(FLT_MAX, FLT_MAX);
    ImGui::SetNextWindowSizeConstraints(minSize, maxSize);
    if (ImGui::Begin("Canary", &悬浮窗, ImGuiWindowFlags_NoCollapse))
    {
      if (!isInitialized)
      {
        if (ImGui::Button("初始化", ImVec2(-1, 60)))
        {
          绘制.初始化绘制("com.tencent.tmgp.pubgmhd", abs_ScreenX, abs_ScreenY);
          绘制.按钮.绘制 = true;
          isInitialized = true;
        }
      }
      else
      {
        if (ImGui::Button("退出", ImVec2(-1, 60)))
        {
          exit(1);
        }
      }
      ImGui::Separator();

      // 左右布局：左侧菜单，右侧内容
      static int selectedMenu = 0; // 当前选中的菜单项
      const char *menuItems[] = {
          "人物绘制", "绘图调节", "颜色配置", "粗细调节", "自瞄面板", "压枪面板"};
      const int menuCount = sizeof(menuItems) / sizeof(menuItems[0]);

      // 左右分栏比例
      const float leftPanelWidth = 190.0f;

      // 开始左侧菜单面板
      ImGui::BeginChild("左侧菜单", ImVec2(leftPanelWidth, 0), true);

      // 添加顶部间距
      ImGui::Dummy(ImVec2(0, 15));

      for (int i = 0; i < menuCount; i++)
      {
        // 计算文本宽度并居中
        float textWidth = ImGui::CalcTextSize(menuItems[i]).x;
        float indentAmount = (leftPanelWidth - textWidth) * 0.5f;
        // 为当前选中项添加高亮样式
        if (selectedMenu == i)
        {
          // 使用ImGui默认Light风格的按钮颜色
          ImGui::PushStyleColor(ImGuiCol_Header, ImVec4(0.26f, 0.59f, 0.98f, 0.40f)); // ImGui Light风格按钮颜色
          ImGui::PushStyleColor(ImGuiCol_HeaderHovered, ImVec4(0.26f, 0.59f, 0.98f, 0.40f));
          ImGui::PushStyleColor(ImGuiCol_HeaderActive, ImVec4(0.26f, 0.59f, 0.98f, 0.67f));
        }
        // 计算居中位置并设置光标位置
        ImGui::SetCursorPosX(indentAmount);
        // 创建一个与文本大小相近的Selectable
        float itemWidth = textWidth + 20; // 在文本两侧各添加10px的间距
        float itemHeight = 45;            // 设置一个适中的高度
        // 确保宽度不超过左侧面板宽度
        if (itemWidth > leftPanelWidth - 10)
          itemWidth = leftPanelWidth - 10;
        // 使用Selectable创建菜单项，设置指定大小
        if (ImGui::Selectable(menuItems[i], selectedMenu == i, ImGuiSelectableFlags_None, ImVec2(itemWidth, itemHeight)))
        {
          selectedMenu = i;
        }
        if (selectedMenu == i)
        {
          ImGui::PopStyleColor(3);
        }
        // 在菜单项之间添加间隔
        ImGui::Dummy(ImVec2(0, 10));
      }

      ImGui::EndChild();

      // 右侧内容面板
      ImGui::SameLine();
      ImGui::BeginChild("右侧内容", ImVec2(0, 0), true);

      // 根据选中的菜单项显示对应的内容
      switch (selectedMenu)
      {
      case 0: // 人物绘制
      {

        if (ImGui::Checkbox("人数", &绘制.按钮.人数))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("方框", &绘制.按钮.方框))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("血量", &绘制.按钮.血量))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("手持", &绘制.按钮.手持))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("雷达", &绘制.按钮.雷达))
          绘制.保存配置();
        if (ImGui::Checkbox("背敌", &绘制.按钮.背敌预警))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("距离", &绘制.按钮.距离))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("射线", &绘制.按钮.射线))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("名字", &绘制.按钮.名字))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("骨骼", &绘制.按钮.骨骼))
          绘制.保存配置();

        ImGui::BulletText("物资绘制");

        if (ImGui::Checkbox("投掷", &绘制.按钮.绘制投掷))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("载具", &绘制.按钮.绘制载具))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("防具", &绘制.按钮.绘制防具))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("道具", &绘制.按钮.绘制道具))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("盒子", &绘制.按钮.绘制盒子))
          绘制.保存配置();

        if (ImGui::Checkbox("药品", &绘制.按钮.绘制药品))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("子弹", &绘制.按钮.绘制子弹))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("武器", &绘制.按钮.绘制武器))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("配件", &绘制.按钮.绘制配件))
          绘制.保存配置();
        ImGui::SameLine();
        if (ImGui::Checkbox("地铁", &绘制.按钮.绘制地铁))
          绘制.保存配置();

        ImGui::BulletText("解密");
        if (ImGui::Button("解密1"))
        {
          绘制.解密模式 = 0x4000;
          绘制.InitMaps();
        }
        ImGui::SameLine();
        if (ImGui::Button("解密2"))
        {
          绘制.解密模式 = 0x20000;
          绘制.InitMaps();
        }
        ImGui::SameLine();
        if (ImGui::Checkbox("忽略人机", &绘制.按钮.忽略人机))
          绘制.保存配置();
        break;
      }
      case 1: // 绘图调节
      {

        if (ImGui::Combo("手持绘图样式", &绘制.按钮.手持绘图, "手持武器文字\0手持武器图片\0"))
          绘制.保存配置();
        if (ImGui::Combo("血条绘图样式", &绘制.按钮.血条绘图, "默认样式\0IOS样式\0OE样式\0"))
          绘制.保存配置();

        if (ImGui::SliderInt("绘制帧率", &绘制.按钮.帧率选项, 60, 144))
          绘制.保存配置();

        if (ImGui::SliderFloat("骨骼显示距离", &绘制.骨骼距离限制, 0.0f, 300.0f, "%.0f", 1))
          绘制.保存配置();
        if (ImGui::SliderFloat("雷达左右位置", &绘制.按钮.雷达X, 0.0f, 2400.0f, "%.1f", 1))
          绘制.保存配置();
        if (ImGui::SliderFloat("雷达上下位置", &绘制.按钮.雷达Y, 0.0f, 1080.0f, "%.1f", 2))
          绘制.保存配置();
        break;
      }
      case 2: // 颜色配置
      {

        static int ColorSettings = 1;
        ImGui::Combo("当前配置", &ColorSettings, "人机\0真人\0");

        if (ColorSettings == 1)
        {
          if (ImGui::ColorEdit4("方框颜色", 绘制.Colorset[0].方框颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("射线颜色", 绘制.Colorset[0].射线颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("骨骼颜色", 绘制.Colorset[0].骨骼颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("血量颜色", 绘制.Colorset[0].血量颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("距离颜色", 绘制.Colorset[0].距离颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("名称颜色", 绘制.Colorset[0].名称颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
        }
        else
        {
          if (ImGui::ColorEdit4("方框颜色", 绘制.Colorset[1].方框颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("射线颜色", 绘制.Colorset[1].射线颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("骨骼颜色", 绘制.Colorset[1].骨骼颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("血量颜色", 绘制.Colorset[1].血量颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("距离颜色", 绘制.Colorset[1].距离颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
          if (ImGui::ColorEdit4("名称颜色", 绘制.Colorset[1].名称颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
            绘制.保存配置();
        }

        if (ImGui::ColorEdit4("手持颜色", 绘制.手持颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
          绘制.保存配置();
        if (ImGui::ColorEdit4("物资颜色", 绘制.物资颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
          绘制.保存配置();
        if (ImGui::ColorEdit4("车辆颜色", 绘制.车辆颜色, ImGuiColorEditFlags_NoBorder | ImGuiColorEditFlags_NoTooltip | ImGuiColorEditFlags_DisplayHex))
          绘制.保存配置();
        break;
      }
      case 3: // 粗细调节
      {
        ImGui::Text("粗细调节");
        ImGui::Separator();

        ImGui::SliderFloat("方框粗细", &绘制.按钮.方框粗细, 0.1, 5, "%.1f");
        ImGui::SliderFloat("射线粗细", &绘制.按钮.射线粗细, 0.1, 5, "%.1f");
        ImGui::SliderFloat("骨骼粗细", &绘制.按钮.骨骼粗细, 0.1, 5, "%.1f");
        break;
      }
      case 4: // 自瞄面板
      {
        static int AimMenu = 0;

        ImGui::BeginChild("##自瞄菜单", ImVec2(-1, 60), false, ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_AlwaysAutoResize);

        if (ImGui::Button("触摸自瞄"))
        {
          AimMenu = 0;
        }
        ImGui::SameLine();
        if (ImGui::Button("调节"))
        {
          AimMenu = 2;
        }
        ImGui::SameLine();
        if (ImGui::Button("打击部位"))
        {
          AimMenu = 1;
        }
        ImGui::SameLine();
        if (ImGui::Button("倍镜力度"))
        {
          AimMenu = 3;
        }

        ImGui::EndChild();
        switch (AimMenu)
        {
        case 0:
        {
          if (ImGui::Checkbox("开启自瞄", &绘制.自瞄.初始化))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("自瞄开关键", &绘制.自瞄.自瞄控件))
            绘制.保存配置();

          if (ImGui::Checkbox("触摸位置", &绘制.自瞄.触摸位置))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("动态范围圈", &绘制.自瞄.动态自瞄))
            绘制.保存配置();

          if (ImGui::Checkbox("准星射线", &绘制.自瞄.准星射线))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("隐藏自瞄圈", &绘制.自瞄.隐藏自瞄圈))
            绘制.保存配置();

          ImGui::BulletText("自瞄设置");
          if (ImGui::Checkbox("人机不瞄", &绘制.自瞄.人机不瞄))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("倒地不瞄", &绘制.自瞄.倒地不瞄))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("触摸虚拟化", &绘制.自瞄.随机触摸点))
            绘制.保存配置();

          ImGui::BulletText("上分功能");

          if (ImGui::Checkbox("掉血自瞄", &绘制.自瞄.掉血自瞄))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("喷子自瞄", &绘制.自瞄.喷子自瞄))
            绘制.保存配置();

          if (ImGui::Checkbox("框内自瞄", &绘制.自瞄.框内自瞄))
            绘制.保存配置();
          ImGui::SameLine();
          if (ImGui::Checkbox("开镜狙击自瞄", &绘制.自瞄.狙击自瞄))
            绘制.保存配置();

          if (ImGui::Checkbox("软锁自瞄", &绘制.自瞄.软锁自瞄))
          {
            if (绘制.自瞄.软锁自瞄)
            {
              绘制.备份.压枪力度 = 绘制.自瞄.压枪力度;
              绘制.备份.自瞄速度 = 绘制.自瞄.自瞄速度;
              绘制.备份.mk20压枪 = 绘制.mk20;

              绘制.自瞄.压枪力度 = 3.5f;
              绘制.自瞄.自瞄速度 = 25.f;
              绘制.mk20 = 0.25f;
            }

            if (!绘制.自瞄.软锁自瞄)
            {
              绘制.自瞄.压枪力度 = 绘制.备份.压枪力度;
              绘制.自瞄.自瞄速度 = 绘制.备份.自瞄速度;
              绘制.mk20 = 绘制.备份.mk20压枪;
            }
          }
          break;
        }
        case 1:
        {
          if (ImGui::Combo("自瞄条件", &绘制.自瞄.自瞄条件, "开火\0开镜\0开火||开镜\0"))
            绘制.保存配置();
          if (ImGui::Combo("喷子自瞄触发条件", &绘制.自瞄.喷子自瞄条件, "持续锁定\0开镜锁定\0"))
            绘制.保存配置();
          if (ImGui::Combo("充电口方向", &绘制.自瞄.充电口方向, "右边\0左边\0"))
            绘制.保存配置();
          if (ImGui::Combo("自瞄优先", &绘制.自瞄.瞄准优先, "准星\0距离\0"))
            绘制.保存配置();
          if (ImGui::Combo("瞄准部位", &绘制.自瞄.瞄准部位, "头部\0胸部\0臀部\0"))
            绘制.保存配置();
          break;
        }
        case 2:
        {
          ImGui::Text("自瞄调节");
          ImGui::SameLine();
          ImGui::TextDisabled("(?)");
          if (ImGui::BeginItemTooltip())
          {
            ImGui::PushTextWrapPos(ImGui::GetFontSize() * 35.0f);
            ImGui::TextUnformatted(
                "参数调节详细\n"
                "1.自瞄速度 增则慢 减则快\n"
                "2.压枪力度 增则下抬 减则上抬\n");
            ImGui::PopTextWrapPos();
            ImGui::EndTooltip();
          }

          if (ImGui::SliderFloat("触摸帧率", &绘制.自瞄.触摸采样率, 200, 1200, "%.0f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("自瞄范围", &绘制.自瞄.自瞄范围, 10, 300, "%.0f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("站蹲压枪力度", &绘制.自瞄.压枪力度, 0.1, 5, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("趴下压枪力度", &绘制.自瞄.趴下位置调节, 0, 2, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("平滑速度", &绘制.自瞄.自瞄速度, 0.f, 80.f, "%.0f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("掉血自瞄概率", &绘制.自瞄.掉血自瞄数率, 5, 25, "%.0f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("预判速度", &绘制.自瞄.预判力度, 0.0, 2.0, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("扫车预判", &绘制.预判度.扫车, 0.0, 4.f, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("腰射自瞄距离", &绘制.自瞄.腰射距离限制, 0.0, 300, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("开镜自瞄距离", &绘制.自瞄.自瞄距离限制, 0.0, 300, "%.2f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("喷子距离限制", &绘制.自瞄.喷子距离限制, 0, 50, "%.0f"))
            绘制.保存配置();
          if (ImGui::SliderFloat("触摸范围大小", &绘制.自瞄.触摸范围, 5, 600, "%.0f"))
            绘制.保存配置();
          break;
        }
        case 3:
        {
          // 倍镜力度调整菜单
          ImGui::Text("== 倍镜瞄准力度调整 ==");
          ImGui::Text("调整倍数会影响所有武器在相应瞄准状态下的压枪力度");

          // 不同瞄准状态的力度调整
          // 腰射状态 (Fov > 75 && Fov <= 130)
          if (ImGui::SliderFloat("腰射力度", &绘制.自瞄.倍镜力度[0], 0.5f, 2.0f, "%.2f"))
            绘制.保存配置();

          // 机瞄状态 (Fov == 70 || Fov == 75)
          if (ImGui::SliderFloat("机瞄力度", &绘制.自瞄.倍镜力度[1], 0.5f, 2.0f, "%.2f"))
            绘制.保存配置();

          // 红点状态 (Fov == 55 || Fov == 60)
          if (ImGui::SliderFloat("红点力度", &绘制.自瞄.倍镜力度[2], 0.5f, 2.0f, "%.2f"))
            绘制.保存配置();

          // 二倍镜状态 (Fov == 44)
          if (ImGui::SliderFloat("二倍力度", &绘制.自瞄.倍镜力度[3], 0.5f, 2.0f, "%.2f"))
            绘制.保存配置();

          // 三倍镜状态 (Fov == 26)
          if (ImGui::SliderFloat("三倍力度", &绘制.自瞄.倍镜力度[4], 0.5f, 2.0f, "%.2f"))
            绘制.保存配置();

          // 四倍镜状态 (Fov == 20)
          if (ImGui::SliderFloat("四倍力度", &绘制.自瞄.倍镜力度[5], 0.5f, 2.0f, "%.2f"))
            绘制.保存配置();

          // 六倍镜状态 (Fov == 13)
          if (ImGui::SliderFloat("六倍力度", &绘制.自瞄.倍镜力度[6], 0.5f, 2.0f, "%.2f"))
            绘制.保存配置();

          // 八倍镜状态 (Fov == 11.03f || (Fov > 11.0f && Fov < 11.1f))
          if (ImGui::SliderFloat("八倍力度", &绘制.自瞄.倍镜力度[7], 0.5f, 2.0f, "%.2f"))
            绘制.保存配置();

          break;
        }
        }
        break;
      }
      case 5: // 压枪面板
      {
        static int selectedWeaponIndex = -1;
        static int selectedStance = 0;
        static float adjustmentValue = 0.01f;

        // 整体面板
        ImGui::BeginChild("##压枪面板", ImVec2(-1, -1), false);

        // 顶部控制栏
        ImGui::BeginChild("##顶部控制", ImVec2(-1, 60), true);
        {
          const float spacing = 10.0f; // 控件之间的间距
          float cursorX = spacing;

          // 姿势选择 Combo
          ImGui::SetCursorPosX(cursorX);
          ImGui::SetCursorPosY((60 - ImGui::GetFrameHeight()) * 0.5f);
          ImGui::SetNextItemWidth(120);
          const char *stances[] = {"站立", "趴下"};
          ImGui::Combo("##姿势", &selectedStance, stances, IM_ARRAYSIZE(stances));

          cursorX += 120 + spacing * 3;

          // 粗调滑块
          ImGui::SetCursorPosX(cursorX);
          ImGui::SetCursorPosY((60 - ImGui::GetFrameHeight()) * 0.5f);
          ImGui::SetNextItemWidth(200);
          if (selectedWeaponIndex >= 0)
          {
            float *targetArray = selectedStance == 0 ? 绘制.自瞄.站压枪 : 绘制.自瞄.趴压枪;
            if (ImGui::SliderFloat("##粗调", &targetArray[selectedWeaponIndex], 0.0f, 2.0f, "%.2f"))
            {
              绘制.保存配置();
            }
          }
          else
          {
            float dummy = 0;
            ImGui::SliderFloat("##粗调", &dummy, 0.0f, 2.0f, "%.2f");
          }

          cursorX += 200 + spacing * 2;

          // +/- 按钮
          ImGui::SetCursorPosX(cursorX);
          ImGui::SetCursorPosY((60 - 40) * 0.5f);
          ImGui::PushStyleVar(ImGuiStyleVar_ButtonTextAlign, ImVec2(0.5f, 0.5f));
          ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(0, 4));
          ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.7f, 0.2f, 1.0f));
          ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.8f, 0.3f, 1.0f));
          if (ImGui::Button("+##细调", ImVec2(40, 40)) && selectedWeaponIndex >= 0)
          {
            float *targetArray = selectedStance == 0 ? 绘制.自瞄.站压枪 : 绘制.自瞄.趴压枪;
            targetArray[selectedWeaponIndex] = std::min(2.0f, targetArray[selectedWeaponIndex] + 0.01f);
            绘制.保存配置();
          }
          ImGui::PopStyleColor(2);
          ImGui::PopStyleVar(2);

          cursorX += 40 + spacing * 3;

          ImGui::SetCursorPosX(cursorX);
          ImGui::SetCursorPosY((60 - 40) * 0.5f);
          ImGui::PushStyleVar(ImGuiStyleVar_ButtonTextAlign, ImVec2(0.5f, 0.5f));
          ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(0, 4));
          ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.7f, 0.2f, 0.2f, 1.0f));
          ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.8f, 0.3f, 0.3f, 1.0f));
          if (ImGui::Button("-##细调", ImVec2(40, 40)) && selectedWeaponIndex >= 0)
          {
            float *targetArray = selectedStance == 0 ? 绘制.自瞄.站压枪 : 绘制.自瞄.趴压枪;
            targetArray[selectedWeaponIndex] = std::max(0.0f, targetArray[selectedWeaponIndex] - 0.01f);
            绘制.保存配置();
          }
          ImGui::PopStyleColor(2);
          ImGui::PopStyleVar(2);

          cursorX += 40 + spacing * 3;

          // 当前值显示
          if (selectedWeaponIndex >= 0)
          {
            ImGui::SetCursorPosX(cursorX);
            ImGui::SetCursorPosY((60 - ImGui::GetTextLineHeight()) * 0.5f);
            float *targetArray = selectedStance == 0 ? 绘制.自瞄.站压枪 : 绘制.自瞄.趴压枪;
            ImGui::Text("当前值: %.2f", targetArray[selectedWeaponIndex]);
          }
        }
        ImGui::EndChild();

        // 武器选择面板
        ImGui::Dummy(ImVec2(0, 5));
        ImGui::BeginChild("##武器选择", ImVec2(-1, -1), true);
        {
          const struct
          {
            const char *name;
            int index;
          } weapons[] = {
              {"AKM", 0}, {"M16A4", 1}, {"SCAR-L", 2}, {"M416", 3}, {"Groza", 4}, {"AUG", 5}, {"QBZ", 6}, {"M762", 7}, {"Mk47", 8}, {"G36C", 9}, {"AC-VAL", 10}, {"蜜獾", 11}, {"FAMAS", 12}, {"UZI", 13}, {"UMP45", 14}, {"Vector", 15}, {"汤姆逊", 16}, {"野牛", 17}, {"MP5K", 18}, {"P90", 19}, {"SKS", 20}, {"VSS", 21}, {"MINI14", 22}, {"MK14", 23}, {"M417", 24}, {"MK20", 25}, {"M249", 26}, {"DP-28", 27}, {"MG3", 28}, {"PKM", 29}};

          float windowWidth = ImGui::GetContentRegionAvail().x;
          float buttonWidth = (windowWidth - 4 * ImGui::GetStyle().ItemSpacing.x) / 5;
          float buttonHeight = 40;

          // 设置按钮文字居中的样式
          ImGui::PushStyleVar(ImGuiStyleVar_SelectableTextAlign, ImVec2(0.5f, 0.5f));

          for (int i = 0; i < IM_ARRAYSIZE(weapons); i++)
          {
            if (i > 0 && i % 5 != 0)
              ImGui::SameLine();

            bool isSelected = selectedWeaponIndex == weapons[i].index;
            if (isSelected)
              ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.3f, 0.5f, 0.7f, 1.0f));

            // 确保按钮文字垂直居中
            float textHeight = ImGui::GetTextLineHeight();
            float buttonPadding = (buttonHeight - textHeight) * 0.5f;
            ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(0, buttonPadding));

            if (ImGui::Button(weapons[i].name, ImVec2(buttonWidth, buttonHeight)))
              selectedWeaponIndex = weapons[i].index;

            ImGui::PopStyleVar();
            if (isSelected)
              ImGui::PopStyleColor();

            if ((i + 1) % 5 == 0)
              ImGui::Dummy(ImVec2(0, 2));
          }

          ImGui::PopStyleVar();
        }
        ImGui::EndChild();
        ImGui::EndChild();
        break;
      }
      }
      ImGui::EndChild();
      绘制.winWidth = ImGui::GetWindowWidth();
      绘制.winHeith = ImGui::GetWindowHeight();
      g_window = ImGui::GetCurrentWindow();

      ImGui::End();
    }
  }

  drawEnd();
}

int GetEventCount()
{
  int count = 0;
  dirent *ptr = NULL;
  DIR *dir = opendir("/dev/input/");
  if (dir == NULL)
  {
    std::cerr << "\033[31;1m[-] Failed to open /dev/input/\033[30;1m" << std::endl;
    return -1;
  }
  while ((ptr = readdir(dir)) != NULL)
  {
    if (strstr(ptr->d_name, "event"))
    {
      count++;
    }
  }
  closedir(dir);
  return count ? count : -1;
}

void 布局::开启悬浮窗()
{
  timer WindowDrawing;
  WindowDrawing.SetFps(绘制.按钮.当前帧率);
  WindowDrawing.AotuFPS_init();
  WindowDrawing.setAffinity();
  int eventcount = GetEventCount();
  if (eventcount <= 0)
  {
    std::cerr << "\033[31;1m[-] 获取音量监听失败\033[30;1m" << std::endl;
    return;
  }
  int *volumedevicefilearray = (int *)malloc(eventcount * sizeof(int));
  for (int i = 0; i < eventcount; i++)
  {
    char inputfilepath[128] = "";
    sprintf(inputfilepath, "/dev/input/event%d", i);
    volumedevicefilearray[i] = open(inputfilepath, O_RDWR | O_NONBLOCK);
  }
  struct input_event event;
  while (true)
  {
    绘制悬浮窗();
    for (int i = 0; i < eventcount; i++)
    {
      memset(&event, 0, sizeof(input_event));
      if (read(volumedevicefilearray[i], &event, sizeof(event)) > 0)
      {
        if (event.type == EV_KEY && event.value == 1) // 按键按下时
        {
          if (event.code == KEY_VOLUMEUP)
          {
            悬浮窗 = true;
          }
          else if (event.code == KEY_VOLUMEDOWN)
          {
            悬浮窗 = false;
          }
        }
      }
    }
    WindowDrawing.SetFps(绘制.按钮.当前帧率);
    WindowDrawing.AotuFPS();
    std::this_thread::sleep_for(1ms);
  }
  // 清理资源
  for (int i = 0; i < eventcount; i++)
  {
    if (volumedevicefilearray[i] > 0)
    {
      close(volumedevicefilearray[i]);
    }
  }
  free(volumedevicefilearray);
}