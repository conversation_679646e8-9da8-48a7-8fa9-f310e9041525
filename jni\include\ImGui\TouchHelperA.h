/*
 * TouchHelperA.h - 触摸助手类头文件（已升级为KMA内核触摸）
 *
 * 修改说明：
 * 1. 已对接KMA驱动的内核触摸功能
 * 2. 新增KMA内核触摸扩展功能
 * 3. 保持原有接口兼容性
 */

#pragma once

#include "VectorStruct.h"

extern int Touch_ID;
extern int Touch_I;
extern int Touch_I_bak;
extern Vector2 Touch_Clicks;
extern int Touch_Global_SLOT;
extern int Touch_Temporary_SLOT;
extern int Touch_Temporary_SLOT_Bak;

// 原有触摸接口（已升级为KMA内核触摸）
bool Touch_Init(int w, int h, uint32_t orientation_, bool readOnly);
void UpdateScreenData(int w, int h, uint32_t orientation_);

void Touch_Close();
void Touch_Down(float x, float y);
void Touch_Move(float x, float y);
void Touch_Up();

// KMA内核触摸扩展功能
void Touch_Click(float x, float y, int delayMs = 50);
void Touch_Swipe(float startX, float startY, float endX, float endY, int steps = 10, int delayMs = 10);

